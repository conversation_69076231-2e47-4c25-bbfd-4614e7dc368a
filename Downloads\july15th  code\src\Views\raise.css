/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    "Inter",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    sans-serif;
  line-height: 1.6;
  color: #1f2a44;
}

/* Main wrapper */
.wrapper {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #6b7280 0%, #3b82f6 100%);
}

/* Section styling */
.section {
  flex: 1;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Toggle button container (from old styles) */
.toggle-button-container {
  max-width: 1280px;
  margin: 20px auto 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: flex-end;
}

/* Raise container */
.raise-container {
  max-width: 1280px; /* Increased for table view (from old styles) */
  margin: 10px auto 50px auto; /* From old styles */
  padding: 2rem;
  background: rgba(255, 255, 255, 0.2); /* Glassmorphism effect */
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Form grid layout */
.raise-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  grid-template-areas:
    "title type"
    "severity module"
    "description description"
    "images images"
    "submit submit";
}

/* Named grid areas */
.form-group:nth-child(1) {
  grid-area: title;
}
.form-group:nth-child(2) {
  grid-area: type;
}
.form-group:nth-child(3) {
  grid-area: severity;
}
.form-group:nth-child(4) {
  grid-area: module;
}
.form-group:nth-child(5) {
  grid-area: description;
}
.paste-area {
  grid-area: images;
}
.raise-button {
  grid-area: submit;
}

/* Responsive design for mobile */
@media (max-width: 768px) {
  .raise-container {
    max-width: 95%; /* From old styles */
    padding: 1.5rem; /* From old styles */
  }

  .toggle-button-container {
    max-width: 95%; /* From old styles */
    padding: 0 15px; /* From old styles */
  }

  .raise-form {
    grid-template-columns: 1fr;
    grid-template-areas:
      "title"
      "type"
      "severity"
      "module"
      "description"
      "images"
      "submit";
  }
}

/* Typography */
h2 {
  font-size: 2rem; /* From old styles */
  font-weight: 600; /* From old styles */
  color: #ffffff; /* Updated for contrast on gradient background */
  margin-bottom: 1.5rem;
  text-align: center;
}

h3 {
  font-size: 1.5rem; /* From old styles */
  font-weight: 600; /* From old styles */
  color: #ffffff; /* Updated for contrast */
  margin-bottom: 1rem;
  text-align: center; /* From old styles */
}

label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #e5e7eb;
  margin-bottom: 0.5rem;
}

/* Form controls */
input,
select,
textarea {
  width: 100%;
  padding: 0.75rem; /* Adjusted for better spacing */
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  resize: vertical; /* From old styles for textarea */
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.3);
  background: rgba(255, 255, 255, 0.15);
}

/* Select with icons */
select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23ffffff' stroke-width='2'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M19 9l-7 7-7-7'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1.25rem;
  padding-right: 2.5rem;
}

/* Paste area */
.paste-area {
  background: rgba(255, 255, 255, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.3); /* Updated from old styles */
  border-radius: 8px; /* From old styles */
  padding: 1.5rem; /* Adjusted from old styles */
  min-height: 150px; /* From old styles */
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer; /* From old styles */
}

.paste-area:focus {
  border-color: #60a5fa;
  background: rgba(255, 255, 255, 0.15);
}

.preview-img {
  max-width: 100px; /* Adjusted for grid */
  max-height: 100px;
  object-fit: cover;
  border-radius: 8px; /* From old styles */
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1); /* From old styles */
}

/* Submit button */
.raise-button {
  height: 35px; /* From old styles */
  padding: 0.75rem 1.5rem;
  background: linear-gradient(
    135deg,
    #007bff,
    #60a5fa
  ); /* Updated with gradient */
  color: #ffffff;
  font-weight: 600; /* From old styles */
  border: none;
  border-radius: 8px; /* From old styles */
  cursor: pointer;
  transition: all 0.3s ease; /* From old styles */
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1rem;
}

.raise-button:hover {
  background: linear-gradient(
    135deg,
    #0056b3,
    #3b82f6
  ); /* Updated from old styles */
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.raise-button:active {
  transform: translateY(0);
}

/* Issues table */
.issues-table-container {
  margin-top: 20px; /* From old styles */
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  padding: 1rem;
  overflow-x: auto; /* From old styles */
  -webkit-overflow-scrolling: touch; /* From old styles */
}

.issues-table-container table {
  font-size: 14px; /* From old styles */
  min-width: 800px; /* From old styles */
  width: 100%;
  border-collapse: collapse;
  background-color: rgba(255, 255, 255, 0.95);
}

.issues-table-container th,
.issues-table-container td {
  white-space: nowrap; /* From old styles */
  padding: 12px 8px;
  border-bottom: 1px solid #e5e7eb;
}

.issues-table-container td:nth-child(3) {
  /* Adjusted for issue_title */
  white-space: normal; /* From old styles */
  word-wrap: break-word; /* From old styles */
  max-width: 200px;
}

/* Modal styles */
.modal-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Form group animation */
.form-group {
  position: relative;
  margin-bottom: 1.5rem; /* From old styles, adjusted */
}

.form-group::before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px;
  background: #60a5fa;
  transition: width 0.3s ease;
}

.form-group:focus-within::before {
  width: 100%;
}

/* Icon styles for select options */
select option[value="Bug"] {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ef4444' viewBox='0 0 24 24'%3E%3Cpath d='M20 8h-2.81a5.985 5.985 0 00-1.82-1.96L17 4.41 15.59 3l-2.17 2.17a6.002 6.002 0 00-2.83 0L8.41 3 7 4.41l1.62 1.63A5.985 5.985 0 006.81 8H4v2h2.09c-.05.33-.09.66-.09 1v1H4v2h2v1c0 .34.04.67.09 1H4v2h2.81a5.985 5.985 0 001.82 1.96L7 19.59 8.41 21l2.17-2.17a6.002 6.002 0 002.83 0L15.59 21 17 19.59l-1.63-1.62a5.985 5.85 0 001.82-1.96H20v-2h-2.09c.05-.33.09-.66.09-1v-1h2v-2h-2v-1c0-.34-.04-.67-.09-1H20V8zM12 6a4 4 0 110 8 4 4 0 010-8z'/%3E%3C/svg%3E");
}
select option[value="Enhancement"] {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%2360a5fa' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z'/%3E%3C/svg%3E");
}
select option[value="Feature Request"] {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%2310b981' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
}

/* Animation for form controls */
@keyframes inputFocus {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

input:focus,
select:focus,
textarea:focus {
  animation: inputFocus 0.3s ease;
}

/* Responsive table styles */
@media (max-width: 768px) {
  .issues-table-container table {
    min-width: 800px; /* From old styles */
    font-size: 12px; /* From old styles */
  }

  .issues-table-container th,
  .issues-table-container td {
    padding: 8px !important; /* From old styles */
  }

  .issues-table-container td:nth-child(3) {
    max-width: 200px; /* From old styles */
  }
}

@media (max-width: 480px) {
  .issues-table-container table {
    min-width: 600px; /* From old styles */
    font-size: 11px; /* From old styles */
  }

  .issues-table-container th,
  .issues-table-container td {
    padding: 6px !important; /* From old styles */
  }
}
