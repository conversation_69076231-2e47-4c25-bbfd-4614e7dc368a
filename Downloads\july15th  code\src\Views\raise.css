.raise-container {
  max-width: 1280px; /* Increased for table view */
  margin: 10px auto 50px auto; /* Removed top margin since button is separate */
  padding: 20px;
  background: #f7f7f7;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.raise-form {
  display: flex;
  flex-direction: column;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

h3 {
  text-align: center;
  font-weight: 600;
  font-size: 1.5rem;
  color: #333;
}

.paste-area {
  border: 2px dashed #ccc;
  padding: 20px;
  min-height: 150px;
  text-align: center;
  cursor: pointer;
  margin-bottom: 20px;
  border-radius: 8px;
  background-color: #fff;
}

.preview-img {
  max-width: 100%;
  margin-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

textarea {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 14px;
  resize: vertical;
}

.raise-button {
  height: 35px;
  background-color: #007bff;
  color: white;
  font-weight: bold;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.raise-button:hover {
  background-color: #0056b3;
}

h2 {
  font-weight: 600;
  font-size: 2rem;
  /* padding-bottom: 1rem; */
}

/* Separate button styles */
.toggle-button-container {
  max-width: 1200px;
  margin: 20px auto 0 auto;
  padding: 0 20px;
}

/* Issues Table Styles */
.issues-table-container {
  margin-top: 20px;
}

.issues-table-container table {
  font-size: 14px;
}

.issues-table-container th,
.issues-table-container td {
  white-space: nowrap;
}

.issues-table-container td:nth-child(2) {
  white-space: normal;
  word-wrap: break-word;
}

/* Responsive table styles */
@media (max-width: 768px) {
  .raise-container {
    max-width: 95%;
    margin: 0 auto 20px auto;
    padding: 15px;
  }

  .toggle-button-container {
    max-width: 95%;
    margin: 10px auto 0 auto;
    padding: 0 15px;
  }

  .issues-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .issues-table-container table {
    min-width: 800px;
    font-size: 12px;
  }

  .issues-table-container th,
  .issues-table-container td {
    padding: 8px !important;
  }

  .issues-table-container td:nth-child(2) {
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .issues-table-container table {
    min-width: 600px;
    font-size: 11px;
  }

  .issues-table-container th,
  .issues-table-container td {
    padding: 6px !important;
  }
}
