import React, { useState, useEffect } from "react";
import LeftNav from "../Components/LeftNav.jsx";
import "../Components/leftnav.css";
import TitleBar from "../Components/TitleBar";
import "../Components/titlenav.css";
import "./Dashboard/UpdateCandidate.css";
import Multiselect from "multiselect-react-dropdown";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useLocation, useNavigate } from "react-router-dom";
import "./AccountCreation/AccountCreation.css";
import { Tooltip as ReactTooltip } from "react-tooltip";
import { FaAngleRight } from "react-icons/fa";
import { ThreeDots } from "react-loader-spinner";
import { IoMdSearch } from "react-icons/io";
import { MdOutlineYoutubeSearchedFor } from "react-icons/md";
import { RiSwapBoxLine } from 'react-icons/ri';
import { FaAngleLeft } from "react-icons/fa6";
import {gettargetAssigned} from "../Views/utilities.js";

// Initialize toast notifications
//toast.configure();



function Targetfix() {
  const [recruiters, setRecruiters] = useState([]);
  const [targetData, setTargetData] = useState([]);
  const [waitForSubmission, setwaitForSubmission] = useState(false);
  const initialState = {
    recruiter: [],
  };
  // this for Pagenation
  const [belowCount, setBelowCount] = useState(0);
  const [countItems, setCountItems] = useState(0);
  const [searchValue, setSearchValue] = useState("");
  const [filteredData, setFilteredData] = useState([]);
  const [id, setId] = useState(1);

  console.log(initialState,"fromfixeddata")
  const [formData, setFormData] = useState(initialState);
  const { targetAssign } = useSelector((state) => state.targetAssignedSliceReducer);
 // console.log(formData, "cxadd")

  useEffect(() => {
    //  fetchAllCandidates();
    gettargetAssigned()
  
  }, []);

  useEffect(() => {
    if (targetAssign) {

      setTargetData(targetAssign)
      setBelowCount(targetAssign.length);
      setFilteredData(targetAssign)
      // gettargetAssigned()

    }
     
  }, [targetAssign]);
  console.log(targetAssign,"all targetvalues")
  useEffect(() => {
    if (Array.isArray(targetData)) {
      const results = targetData.filter((item) =>
        Object.values(item).some((val) =>
          val?.toString().toLowerCase().includes(searchValue.toLowerCase())
        )
      );
    setFilteredData(results);
    setBelowCount(results.length); // Update count of filtered results
    }
  }, [searchValue, targetData]);
  useEffect(() => {
    if (belowCount % 50 != 0) setCountItems(parseInt(belowCount / 50) + 1);
    else setCountItems(parseInt(belowCount / 50));
  }, [belowCount]);
  const [showTable, setShowTable] = useState(false);

  const handleSwapClick = () => {
    setShowTable((prev) => !prev); // Toggle table visibility
    gettargetAssigned()
  };

  useEffect(() => {
    // console.log(showRecruiters);
    // console.log("1");
    if (
      recruiters.length > 1 &&
      !recruiters.includes("Select All") &&
      formData.recruiter.length !== recruiters.length
    ) {
      // console.log("3");
      setRecruiters((prev) => ["Select All", ...prev]);
    }
  }, [recruiters]);
 
  const handleChangeRecruiter = (selectedList) => {
    let selectAll = false;
    if (
      recruiters.includes("Select All") &&
      selectedList.length === recruiters.length - 1
    ) {
      selectAll = true;
    }
    selectedList.map((item) => {
      if (item.name === "Select All") {
        selectAll = true;
      }
    });
 
    let selectedRecruiters = [];
    if (
      selectAll ||
      selectedList.map((item) => item.name).includes("Select All")
    ) {
      selectedRecruiters.push(...recruiters.slice(1));
      setRecruiters((prev) => [...prev.slice(1)]);
    } else {
      selectedRecruiters.push(...selectedList.map((item) => item.name));
      if (!recruiters.includes("Select All")) {
        setRecruiters((prev) => ["Select All", ...prev]);
      }
    }
 
    // if((recruiters.includes("Select All") && recruiters.length===selectedRecruiters.length+1)
    //    ){
    //    setRecruiters((prev) => [...prev.slice(1)]);
    //  }
 
    // console.log("udpaetd recruiters",  [...recruiters.slice(1)])
    // console.log("recruiters", recruiters);
    // console.log("selectedList", selectedList);
    // console.log("selectedList len", selectedList.length);
    // console.log("selectedRecruiters", selectedRecruiters);
    setFormData({
      ...formData,
      recruiter: [...selectedRecruiters],
    });
 
    // setShowRecruiters(selectedRecruiters.length > 0 ? 3 : 1);
  };

  const { recruiters: rdxRecruiters, managers } = useSelector(
    (state) => state.userSliceReducer,
  );

  //console.log(rdxRecruiters,"allrecruitersby recruting")
 // console.log(recruiters,"all recruiters")
 
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        let arr = [];
        for (const item of rdxRecruiters) {
          if (item["is_active"] && item["is_verified"]) {
            arr.push(item["name"]);
          }
        }
        setRecruiters([...arr]);
      } catch (err) {
        console.error("Error fetching users:", err); // Log any errors
      }
    };

    fetchUsers(); // Call the fetch function inside useEffect
  }, [rdxRecruiters]);

  
  // console.log(recruiters,"allrecruiters")
  // const [selectedRecruiters, setSelectedRecruiters] = useState([]);

  // const handleChangeRecruiter = (selectedList) => {
  //   setSelectedRecruiters(selectedList.map(item => item.name)); // Get recruiter names
  // };
  // Array to store selected recruiters
  const [targetGiven, setTargetGiven] = useState(""); // For target number input

  const [days, setDays] = useState("");

  const handleTargetChange = (e) => {
    setTargetGiven(e.target.value);
  };

  // console.log(targetData,"target datas")

  // Handle day selection
  const handleDaysChange = (e) => {
    setDays(e.target.value);
  };


  const handleSubmittarget = async (e) => {
    e.preventDefault();
    if (recruiters.length === 0) {
      toast.error("Please select at least one recruiter");
      return;
    }

    if (!days) {
      toast.error("Please select a valid day");
      return;
    }
    if (!waitForSubmission) {
      setwaitForSubmission(true);
  

    // Prepare the payload for backend
    console.log(formData.recruiter,"selectedrecruter")
    const payload = {
      recruiter: formData.recruiter, // Send array of recruiter names
      target_given: targetGiven,
      days: days
    };
    console.log("Payload being sent:", payload);
    try {
      const response = await fetch(
        "http://192.168.0.47:5002/recruiter_target",
        // "http://192.168.0.47:5002/login/management",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        },
      );
      if (response.ok) {
        const responsedata = await response.json();
      console.log("Response:", responsedata);
    
          // toast.success(responseData.success_message);
          toast.success(responsedata.message);

        setwaitForSubmission(false);
        //setShowTable(true)
        setFormData({
          recruiter: [], // Reset the recruiter field in formData
        });
        setDays("")
        setTargetGiven("")

     
      setwaitForSubmission(false);
   
      
      }
      
    } catch (error) {
      // Handle any errors (e.g., show error message)
      console.error("Error submitting the form:", error);
    }
  }
  };

  // console.log(days,"selected days")
   console.log(formData.recruiter,"selected Recruiter")
  // console.log(targetGiven,"selected days")
// the filter section is start ------------------------------------------------------------


  const removeAllFilter = () => {
    setRecruitrerSelected([]) 
    setTargetSelected([]) 
    setdaysSelected([]) 
    setTargetcompleteSelected([]) 
    setClosurerateSelected([])  
  }

  // this for pagenation -----------------------------

  const getPageRange = () => {
    const pageRange = [];
    const maxPagesToShow = 5; // Adjust this value to show more or fewer page numbers

    let startPage = Math.max(1, id - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(countItems, startPage + maxPagesToShow - 1);

    if (endPage - startPage < maxPagesToShow - 1) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    if (startPage > 1) {
      pageRange.push(1);
      if (startPage > 2) {
        pageRange.push("...");
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pageRange.push(i);
    }

    if (endPage < countItems) {
      if (endPage < countItems - 1) {
        pageRange.push("...");
      }
      pageRange.push(countItems);
    }

    return pageRange;
  };


  const goToPage = (pageNumber) => {
    if (pageNumber >= 1 && pageNumber <= countItems) {
      setId(pageNumber);
    }
  };
  
  // const handlesearch = (e) => {
  //   const value = e.target.value.toLowerCase(); // Convert search input to lowercase
  //   setSearchValue(value);
 
  //   const filtered = tableData.filter((item) =>
  //     item.recruiter?.toLowerCase().includes(value) || // Convert recruiter field to lowercase
  //     item.target.toString().includes(value) ||
  //     item.days.toString().includes(value) ||
  //     item.completed.toString().includes(value) ||
  //     item.closureRate.toString().includes(value)
  //   );
 
  //   setFilteredData(filtered);
  // };

  
  return (
    <div className="wrapper">
      <LeftNav />
      <div className="section">
        <TitleBar />
        <div className="mobiledash">
          <label
            style={{
              marginTop: "1vh",
              fontWeight: "500",
              paddingRight: "5px",
            }}
          >
            {/* search */}
          </label>

          <div className="searchfield" style={{ display: 'flex', alignItems: 'center', zIndex: '2' }}>
            <IoMdSearch
              style={{ height: "22px", width: "22px", marginRight: "-26px" }}
            />

            <input
              placeholder="Search"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              style={{
                marginTop: "4px",
                paddingLeft: "26px",
                height: "30px",
                width: "250px",
                backgroundColor: "rgba(255, 255, 255, 0.80)",
                border: "none",
                borderRadius: "5px",
                padding: "0 25px",
              }}
              className="Search"
              
            />

            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                marginLeft: '10px',
                padding: '3px',
                borderRadius: "5px",
                marginTop: "4px",
              }}
            >
              <div
                className="remove_filter_icons"
                onClick={() => setSearchValue('')}
                style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
              >
                <MdOutlineYoutubeSearchedFor
                  style={{ height: '24px', width: "24px", color: "#32406d" }}
                  data-tooltip-id={"remove_search"}
                  data-tooltip-content="Clear search"
                />
                <ReactTooltip
                  style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                  place="top-start"
                  id="remove_search"
                />
              </div>

              <div
                className="remove_filter_icons"
                onClick={removeAllFilter}
                style={{ cursor: 'pointer', display: 'flex', alignItems: 'center', marginLeft: '10px' }}
              >
               
                <ReactTooltip
                  style={{ zIndex: 999, padding: "4px", backgroundColor: "#32406d" }}
                  place="top-start"
                  id="remove_filter"
                />
              </div>

              <div
                className="remove_filter_icons"
                style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                  marginLeft: '10px'
                }}
              >
                <RiSwapBoxLine
                  style={{
                    cursor: 'pointer',
                    height: '25px',
                    width: "25px",
                    color: "#32406d",
                  }}
                  data-tooltip-id={"Swap-id"}
                  data-tooltip-content="Swap-id"
                  onClick={handleSwapClick}
                />
                <ReactTooltip
                  style={{
                    zIndex: 999,
                    padding: "2px",
                    backgroundColor: "#32406d",
                  }}
                  place="top-start"
                  id="Swap"
                />
              </div>
            </div>
          </div>
        </div>
        {!showTable ? (
        <section className="signup" style={{ marginTop: "-20px" }}>
          <div className="containeracco2" >
            <div className="signup-content2">
              <form onSubmit={handleSubmittarget} className="signup-form">
                <h2 className="form-title">Assign Target</h2>
                <div
                  className="form-group"
                  style={{
                    padding: "3px",
                    margin: "0",
                    marginBottom:"5px"
                  }}
                >
                  <label className="acclabel" style={{ marginBottom: "5px" }}>Select Recruiter:</label>
                  <div className="input-icon">
                    <div className="recruiter-selection2">
                      <Multiselect
                        // Convert recruiters array into format required by Multiselect
                        //    selectedValues={formData.recruiter.map((item) => ({
                        //      name: item,
                        //  }))} // Convert selected recruiters into format required by Multiselect
                        // selectedValues={formData.recruiter.map((item) => ({
                        //   name: item,
                        // }))} 
                        options={recruiters.map((item) => ({ name: item }))}
                        selectedValues={formData.recruiter.map((item) => ({
                          name: item,
                        }))}
                        onSelect={handleChangeRecruiter}
                        onRemove={handleChangeRecruiter}
                        displayValue="name"
                        style={{
                          searchWrapper: { minHeight: "10px", overflowX: "auto", whiteSpace: "nowrap", padding: "2px" },
                          multiselectContainer: {},
                          optionListContainer: { position: "absolute", zIndex: 1000, top: "100%", border: "1px solid #ccc", maxHeight: "200px", overflowY: "auto" },
                          chip :{
                            background:"green",
                            padding: "4px 100px"}
                        
                        }} // Adjust width as needed
                        
                       
                        placeholder="Select recruiters"
                        className="custom-multiselect"
                      />
                    </div>

                  </div>

                </div>
                <div
                  className="form-group"
                  style={{
                    padding: "3px",
                    margin: "0",
                    marginTop: "50px"
                  }}
                >
                  <label className="acclabel" style={{ marginBottom: "5px" }}>Target:</label>
                  <div className="input-icon">
                    <input
                      type="text"
                      name="name"
                      className="form-int"
                      value={targetGiven}
                      style={{borderRadius:"0px"}}
                      onChange={handleTargetChange}
                      required
                    />

                  </div>

                </div>
                <div
                  className="form-group"
                  style={{
                    padding: "3px",
                    margin: "0",
                       marginBottom:"5px"
                  }}
                >
                  <label className="acclabel" style={{ marginBottom: "5px" }}>Days:</label>
                  <div className="input-icon">
                    <select name="" id="" style={{ height: "40px",borderRadius:"0px" }} value={days} onChange={handleDaysChange} >
                      <option value="" selected disabled> Select</option>
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                      <option value="quarterly">Quartely</option>

                    </select>

                  </div>

                </div>


                <div className="form-group">
                  <button
                    value="Create Account"
                    name="submit"
                    id="submit"
                    style={{ height: "40px" }}
                    className="form-submit"
                  >
                    {waitForSubmission ? "" : "Assign"}

                    <ThreeDots
                      wrapperClass="ovalSpinner"
                      wrapperStyle={{
                        position: "absolute",
                        left: "180px",
                        bottom: "-3px",
                      }}
                      visible={waitForSubmission}
                      height="45"
                      width="45"
                      color="white"
                      ariaLabel="oval-loading"
                    />
                  </button>
                </div>
              </form>
            </div>
          </div>
        </section>
         ) : (
          <>
          <div>
            <h5
              id="theader"
              className="joblisthead"
              style={{ padding: "0px", fontSize: "18px", fontWeight: "700", margin: "-35px 0 10px" }}
            >
              Assign Target Table
            </h5>
          </div>
          <div className="dashcontainer" style={{
            position: "relative",
            width: "100%",
            padding: "5px 5px",
            background: "rgba(255, 255, 255, 0.25)",
            boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
            backdropFilter: "blur(11.5px)",
            borderRadius: "10px",
            border: "1px solid rgba(255, 255, 255, 0.18)",

            overflow: "hidden",
          }}>

            <div style={{ overflowX: 'auto' }}>
              <table className="max-width-fit-content table" style={{ width: "100%", tableLayout: "fixed", marginTop: "0" }} id="candidates-table">
                <thead>
                  <tr style={{ backgroundColor: '#32406d', color: 'white' }}>
                    <th style={{ width:"241px",padding: "12px", border: "1px solid #ddd" }}>Recruiter</th>
                    <th style={{ width:"190px",padding: "12px", border: "1px solid #ddd" }}>Target</th>
                    <th style={{ width:"190px",padding: "12px", border: "1px solid #ddd" }}>Days</th>
                    <th style={{ width:"190px",padding: "12px", border: "1px solid #ddd" }}>Target Completed</th>
                    <th style={{width:"190px", padding: "12px", border: "1px solid #ddd" }}>Closure Rate</th>
                    {/* <th style={{width:"190px", padding: "12px", border: "1px solid #ddd" }}>Credits</th> */}
                  </tr>
                </thead>
                <tbody className="scrollable-body">
                {filteredData?.length === 0 ? (
                    <tr style={{zIndex:"-2"}}>
                      <td colSpan="5" style={{ textAlign: 'center' }}>No data available in table</td>
                    </tr>
                  ) : (
                    Array.isArray(filteredData) && filteredData.slice((id - 1) * 50, id * 50).map((item, idX) => (
                      <tr key={item.id} style={{ backgroundColor: 'white', height: '10px' }}>
                      <td style={{
                          padding: "5px",
                          borderBottom: "1px solid #ddd",
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                          textAlign:"left",
                        }}>{item.recruiter}</td>
                      <td style={{
                          padding: "5px",
                          borderBottom: "1px solid #ddd",
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                        }}>{item.target_given}</td>
                      <td style={{
                          padding: "5px",
                          borderBottom: "1px solid #ddd",
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                        }}>{item.days}</td>
                      <td style={{
                          padding: "5px",
                          borderBottom: "1px solid #ddd",
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                        }}>{item.target_completed}</td>
                      <td style={{
                          padding: "5px",
                          borderBottom: "1px solid #ddd",
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                        }}>{Number(item.closure_rate) % 1 === 0 
    ? item.closure_rate 
    : Number(item.closure_rate).toFixed(2)}%</td>
                          {/* <td style={{
                          padding: "5px",
                          borderBottom: "1px solid #ddd",
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                        }}>0</td> */}
                    </tr>
                      )))}
                </tbody>
              </table>
            </div>

          </div>
          <div
            style={{

            }}
            className="dashbottom"
          >
            <div>
              Showing {belowCount === 0 ? 0 : (id - 1) * 50 + 1} to{" "}
              {id * 50 <= belowCount ? id * 50 : belowCount} of {belowCount}{" "}
              entries
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginTop: "10px",
              }}
              className="pagination"
            >
              <ul className="page">
                <li
                  className="page__btn newpage_btn"
                  style={{
                    padding: "1px 5px",
                    marginRight: "5px",
                    cursor: "pointer",
                    alignItems: "center",
                    color: "#32406d",
                  }}
                  onClick={() => {
                    id !== 1 ? setId(id - 1) : setId(id);
                  }}
                >
                  <FaAngleLeft style={{ marginTop: "3px" }} />
                </li>
                <div className="gap" style={{ display: "flex", columnGap: "10px" }}>

                  {getPageRange().map((pageNumber, index) => (
                    <button
                      className={
                        pageNumber === id ? "pag_buttons" : "unsel_button"
                      }
                      key={index}
                      onClick={() => goToPage(pageNumber)}
                      style={{
                        fontWeight: pageNumber === id ? "bold" : "normal",
                        marginRight: "10px",
                        color: pageNumber === id ? "white" : "#000000", // Changed text color
                        backgroundColor:
                          pageNumber === id ? "#32406d" : "#ffff", // Changed background color
                        borderRadius: pageNumber === id ? "0.2rem" : "",
                        fontSize: "15px",
                        border: "none",
                        padding: "1px 10px", // Adjusted padding
                        cursor: "pointer", // Added cursor pointer
                      }}
                      class="page__numbers"
                    >
                      {pageNumber}
                    
                    </button>
                  ))}

                </div>
                <li
                  className="page__btn newpage_btn"
                  style={{
                    padding: "1px 5px",
                    cursor: "pointer",
                    color: "#32406d",
                    marginLeft: "3px"
                  }}
                  onClick={() => {
                    if (belowCount > id * 50) setId(id + 1);
                    else {
                      toast.warn("Reached the end of the list", {
                        position: "top-right",
                        autoClose: 3000,
                        hideProgressBar: false,
                        closeOnClick: true,
                        pauseOnHover: true,
                        draggable: true,
                        progress: undefined,
                        theme: "dark",
                        transition: Bounce,
                      });
                      setId(id);
                    }
                  }}
                >
                  <FaAngleRight style={{ marginTop: "3px" }} />
                </li>
              </ul>
            </div>
          </div>
        </>
      )}
      </div>
    </div>
  );
}

export default Targetfix;
