import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  base: "/",
  server: {
    port: 1313,
    cors: true,
    host: "0.0.0.0",
    proxy: {
      "/api": {
        // target: "http://vms.makonissoft.com:1919/", //server
        // target: "/api", // my local
        // target: 'http://************:1919', // Ganesh local
        target: 'http://************:5002/',
        // target: "http://************:5000",
            // target: "http://************:5000",

        // my local
        changeOrigin: true,
        // secure: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
    },
  },
});


// export default {
//   build: {
//     rollupOptions: {
//       output: {
//         manualChunks: {
//           'react-vendor': ['react', 'react-dom'],
//           'chart-libs': ['recharts', 'chart.js'],
//         },
//       },
//     },
//   },
// };



// import { defineConfig } from "vite";
// import react from "@vitejs/plugin-react";

// export default defineConfig({
//   plugins: [react()],
//   base: "/",
//   server: {
//     port: 1313,
//     cors: true,
//     host: "0.0.0.0",
//     proxy: {
//       "/api": {
//         target: "http://************:5002/",
//         changeOrigin: true,
//         rewrite: (path) => path.replace(/^\/api/, ""),
//       },
//     },
//   },
//   build: {
//     chunkSizeWarningLimit: 1500, // increases the warning threshold
//     rollupOptions: {
//       output: {
//         manualChunks(id) {
//           if (id.includes("node_modules")) {
//             if (id.includes("react")) return "react";
//             if (id.includes("chart.js")) return "chartjs";
//             if (id.includes("recharts")) return "recharts";
//             return "vendor";
//           }
//         },
//       },
//     },
//   },
// });
