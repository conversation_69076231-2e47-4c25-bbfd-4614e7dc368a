import React, { useState, useEffect, useRef } from "react";
import "../Components/titlenav.css";
import TitleBar from "../Components/TitleBar";
import LeftNav from "../Components/LeftNav";
import Cookies from "universal-cookie";
import { useSelector, useDispatch } from 'react-redux';
import { getAllJobs } from "./utilities";
import { motion } from 'framer-motion';
import * as XLSX from "xlsx";
import { FaFileExcel } from "react-icons/fa";
import { ThreeDots } from "react-loader-spinner";
import { FcBarChart } from "react-icons/fc";
// import Heatmap from "../Components/HeatMap";
import BargraphOV from "../Components/BargraphOV"
import "../Views/Overview.css";
import RadarChart from "../Components/RadarChart";
import DualGaugeCharts from './DualGaugeCharts';
import ScatterPlot from "./ScatterPlot";
// import RelevanceScoreChart from "./RelevanceScoreChart";
import Timelinechart from "../Components/Timelinechart";
import { toast } from 'react-toastify';
import Modal from 'react-modal';
import Timeline from "./data/Timeline.jsx";
import html2canvas from 'html2canvas'; 
import zIndex from "@material-ui/core/styles/zIndex.js";

const cookies = new Cookies();

function OverView() {
    const userName = localStorage.getItem("user_name");
    const recruiterUserID = localStorage.getItem("recruiterUserID");
    const [candidateName, setCandidateName] = useState('');
    const [candidateExperience, setCandidateExperience] = useState('');
    const [skillsMatching, setSkillsMatching] = useState('');
    const [jobDescriptionExperience, setJobDescriptionExperience] = useState('');
    const [jobDescriptionPackage, setJobDescriptionPackage] = useState('');
    const [budgetRange, setBudgetRange] = useState('');
    const jobIdRef = useRef(null);
    const [showData, setShowData] = useState([]);
    const chartRef = useRef(null);
    const [labels, setLabels] = useState([]);
    const [data, setData] = useState([]);
    const { jobs } = useSelector((state) => state.jobSliceReducer);
    const [selectedFile, setSelectedFile] = useState(null);
    const [selectedJobId, setSelectedJobId] = useState('');
    const [search, setSearch] = useState("");
    const [showText, setShowText] = useState(false);
    const [select, setSelect] = useState(false);
    const [client, setClient] = useState("");
    const [clickedData, setClickedData] = useState({
        category: 'Select a bar',
        count: 0,
        items: []
    });
    const [domains, setDomains] = useState([]);
    const [showTable, setShowTable] = useState(true);
    const [showDetails, setShowDetails] = useState(false);
    const handleShowTable = () => {
        setShowTable(prevShowTable => !prevShowTable);
    };
    const handleShowDetails = () => {
        setShowDetails(prevShowDetails => !prevShowDetails);
    };
    const getRandomColor = () => {
        const letters = '0123456789ABCDEF';
        let color = '#';
        for (let i = 0; i < 6; i++) {
            color += letters[Math.floor(Math.random() * 16)];
        }
        return color;
    };
    useEffect(() => {
        const handleClick = (e) => {
            if (
                document.getElementById("drop_down") &&
                document.getElementById("drop_down").contains(e.target)
            ) {
                setSearch(e.target.textContent);
            }
            if (e.target !== document.getElementById("input_field")) {
                setShowText(false);
            }
        };
        window.addEventListener("click", handleClick);
        return () => window.removeEventListener("click", handleClick);
    }, []);

    // const [selectedPrompt, setSelectedPrompt] = useState('');

    useEffect(() => {
        if (jobs.length > 0) {
            console.log(jobs)
            const jobsList = jobs?.filter(job => job.recruiter.includes(localStorage.getItem("name")) && job.job_status === 'Active')
            console.log(jobsList.length)
            console.log(jobsList)
            setData(jobsList);
            setShowData(jobsList);
        } else {
            getAllJobs();
        }
    }, [jobs]);

    const handleResumeChange = (e) => {
        setSelectedFile(e.target.files[0]);
    };

    const handleJobChange = (e) => {
        setSelectedJobId(e.target.value);
    };
    const [showModal, setShowModal] = useState(false);
    const [ExperienceshowModal, setExperienceShowModal] = useState(false);
    const [modalContent, setModalContent] = useState(null);
    const [activeCard, setActiveCard] = useState(null);
    const handleCloseModal = () => {
        setShowModal(false);
        setExperienceShowModal(false);
        setModalContent(null);
        setShowTable(false);
        setActiveCard(null);
        setClickedData({
            category: 'Select a bar',
            count: 0,
            items: []
        });
        setShowRadarChart(false);
    };
    const handleCardClick = (cardType) => {
        setActiveCard(cardType);
        setShowModal(true);
    };

    // const handlePromptChange = (e) => {
    //     setSelectedPrompt(e.target.value);
    // };
    const [showRadarChart, setShowRadarChart] = useState(false);

    const handleLinkClick = (e) => {
        e.preventDefault();
        setShowRadarChart(prevState => !prevState);
        setShowRadarChart(!showRadarChart);
    };
    const fileToBase64 = (file) => {
        if (file === null) return;
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => {
                resolve(reader.result.split(",")[1]);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
            console.log(file, "pdf")
        });
    };
    const [waitForSubmission, setwaitForSubmission] = useState(false);
    const [categoriesCounts, setCategoriesCounts] = useState([]);
    const [candidateInfo, setCandidateInfo] = useState({});
    const [skillsData, setSkillsData] = useState([]);
    const [timelineData, setTimelineData] = useState([]);
    const [Learningattitude, setLearningattitude] = useState([]);
    const [events, setEvents] = useState([])
    const [allCompanies, setAllCompanies] = useState([]);
    const [allSkills, setAllSkills] = useState([]);
    const [allCertificates, setAllCertificates] = useState([]);
    const [modalMessage, setModalMessage] = useState("");
    const [bulletPoints, setBulletPoints] = useState({});
    const [summaryParagraph, setSummaryParagraph] = useState('');

    const extractYears = (experienceString) => {
        // Regular expression to match numeric values (both years and months)
        const match = experienceString.match(/(\d+)(?:\s*years)?(?:\s*\d+)?(?:\s*months)?/i);
        if (match) {
            return parseInt(match[1], 10);
        }
        return 0;
    };

    const extractNumberFromPercentage = (percentageString) => {
        const match = percentageString.match(/(\d+(\.\d+)?)%?/);
        if (match) {
            return parseFloat(match[1]);
        }
        return 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!search) {
            toast.warn("Please select JobID");
            return;
        }
        if (!selectedFile) {
            toast.warn("Please select a resume.");
            return;
        }
        setwaitForSubmission(true);
        try {
            const base64String = await fileToBase64(selectedFile);

            const requestData = {
                user_id: localStorage.getItem("user_id"),
                job_id: search,
                resume: base64String,
            };

            const response = await fetch("http://192.168.0.47:5002/candidate_over_view", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(requestData),
            });

            const data = await response.json();
            console.log("Response:", data);
            setwaitForSubmission(false);

            // Extracting data
            const expertiseResponse = data.expertise_response || {};
            const learningattitudeResponse = data.candidate_learning_response || {};
            const technologyCounts = Object.entries(learningattitudeResponse["Technologies Used"] || {}).map(([company, technologies]) => ({
                company,
                count: technologies.length,
                skills: technologies.join(", "),
            }));

            const categoriesCounts = (expertiseResponse.categories || []).map((item) => ({
                category: item.Category,
                count: item.Items.length,
                items: item.Items.join(", "), // Converts array to a comma-separated string
            }));

            const domains = (expertiseResponse.domains || []).map((item) => ({
                Domain: item.Domain,
                description: item.Description,
            }));

            console.log("Domains:", domains);
            setDomains(domains);

            console.log("Categories and Counts:", categoriesCounts);
            console.log("Learning attitude:", technologyCounts);

            setCategoriesCounts(categoriesCounts);
            setLearningattitude(technologyCounts);

            const allCompanies = technologyCounts.map((item) => item.company);
            const allSkills = [...new Set(technologyCounts.flatMap((item) => item.skills.split(", ")))];
            const allCertificates = learningattitudeResponse["Certifications"] || [];

            console.log("All Companies:", allCompanies);
            console.log("All Skills:", allSkills);
            console.log("All Certificates:", allCertificates);

            setAllCompanies(allCompanies);
            setAllSkills(allSkills);
            setAllCertificates(allCertificates);

            const analyzedata = data.analyze_candidate_profile_response || [];
            const categorizeExperience = (score) => {
                if (score >= 4 && score <= 5) {
                    return "High";
                } else if (score === 3) {
                    return "Moderate";
                } else if (score >= 1 && score <= 2) {
                    return "Low";
                } else {
                    return "Unknown";
                }
            };

            const skillsData = analyzedata.map((item) => ({
                Experience: categorizeExperience(item["Relevance Score"]) || "N/A",
                "Relevance Score": item["Relevance Score"] || 0,
                "Skill/Domain": item["Skill/Domain"] || "Unknown",
            }));

            console.log("Skills Data for Scatter Plot:", skillsData);
            setSkillsData(skillsData);

            const jobInfo = data.job_info_response;
            console.log("Job Info:", jobInfo);

            // Utility functions for data extraction and cleaning
            const extractNumber = (value) => {
                if (Array.isArray(value)) value = value[0]; // Extract first element if array
                if (typeof value === "string") {
                    // Remove non-numeric characters except decimal points
                    const numericValue = value.replace(/[^0-9.]/g, "");
                    return parseFloat(numericValue) || 0;
                }
                return Number(value) || 0; // Default to numeric conversion
            };

            const extractYears = (value) => {
                if (Array.isArray(value)) value = value[0]; // Extract first element if array
                return parseInt(value, 10) || 0;
            };

            const extractNumberFromPercentage = (value) => {
                if (Array.isArray(value)) value = value[0];
                if (typeof value === "string") {
                    return parseFloat(value.replace(/[^0-9.]/g, "")) || 0;
                }
                return Number(value) || 0;
            };

            const JdMaxBudget = extractNumber(jobInfo["Job Description Max Package (LPA)"]);
            const JdMinBudget = extractNumber(jobInfo["Job Description Min Package (LPA)"]);

            const MinExperience = extractYears(jobInfo["Job Description Min Experience"]);
            const MaxExperience = extractYears(jobInfo["Job Description Max Experience"]);

            const SkillsPercentage = extractNumberFromPercentage(
                jobInfo["Skills Matching Percentage"]
            );
            const CandiExperiencePercentage = extractNumberFromPercentage(
                jobInfo["Candidate Experience Percentage"]
            );
            const AddSkill = (SkillsPercentage + CandiExperiencePercentage) / 200;

            const CandidateExperience = extractYears(jobInfo["Candidate Experience"]);

            console.log("Candidate Experience:", CandidateExperience);
            console.log("Min Experience:", MinExperience);
            console.log("Max Experience:", MaxExperience);
            console.log("JD Min Budget:", JdMinBudget);
            console.log("JD Max Budget:", JdMaxBudget);
            // console.log("candidatename:",candidate)
            console.log("AddSkill:", AddSkill);

            let minBudget, maxBudget;

            if (CandidateExperience >= MinExperience && CandidateExperience <= MaxExperience) {
                maxBudget =
                    JdMaxBudget -
                    (JdMaxBudget - JdMinBudget) / 2 * (1 - AddSkill);
                minBudget =
                    JdMinBudget +
                    (JdMaxBudget - JdMinBudget) / 2 * AddSkill;
                maxBudget = maxBudget.toFixed(2);
                minBudget = minBudget.toFixed(2);

                console.log(`Condition met. Calculated maxBudget: ${maxBudget}`);
                console.log(`Condition met. Calculated minBudget: ${minBudget}`);
                setModalMessage("");
            } else {
                console.log(`Condition not met. Candidate Experience: ${CandidateExperience}`);
                console.log(`Min Experience: ${MinExperience}`);
                console.log(`Max Experience: ${MaxExperience}`);
                minBudget = 0;
                maxBudget = 0;
                setModalMessage("Candidate experience is not matching for the assigned budget.");
            }

            const formatNumber = (number) => {
                return Number.isInteger(number) ? `${number}` : number.toFixed(2);
            };

            const candidateInfo = {
                candidate: Array.isArray(jobInfo.Candidate) ? jobInfo.Candidate[0] : "N/A",
                JdMinBudget,
                JdMaxBudget,
                maxBudget,
                minBudget,
                CandidateExperience,
                Jdexperience: `${MinExperience} - ${MaxExperience}`,
                JdPackage: `${formatNumber(JdMinBudget)} - ${formatNumber(JdMaxBudget)}`,
                SkillsPercentage,
                CandiExperiencePercentage,
                AddSkill,
            };

            console.log("Candidate Information:", candidateInfo);
            //    console.log("Candidate Information3:", JSON.parse(jobInfo.categories)?.Candidate?.[0] || "N/A",  );
            setCandidateInfo(candidateInfo);

            console.log("Candidate Information:", candidateInfo);
            const candidateLearningText = data.candidate_learning_textual_representation || {};
            const bulletPoints = candidateLearningText.BulletPoints || {};
            const summaryParagraph = candidateLearningText.SummaryParagraph || "";

            console.log("BulletPoints:", bulletPoints);
            console.log("SummaryParagraph:", summaryParagraph);

            setBulletPoints(bulletPoints);
            setSummaryParagraph(summaryParagraph);

            const jobExperience = data.career_progress_response || [];
            const timelineData = jobExperience.map((item) => ({
                Company: item.Company || "Unknown",
                FromDate: item["From Date"] || "N/A",
                ToDate: item["To Date"] || "Present",
                Location: item.Location || "NA",
                Project: item.Project || "NA",
                Role: item.Title || "N/A",
                Duration: item["Total Duration of Work"] || "N/A",
            }));
            console.log("career Growth", timelineData)
            setEvents(timelineData);

        } catch (error) {
            console.error("Error:", error.message);
            setwaitForSubmission(false);
        }
    };

    const exportLearningAttitude = () => {
        if (!Learningattitude || Learningattitude.length === 0) {
            toast.warn(" Learning Attitude data is unavailable to export!");
            return;
        }

        const candidateName = candidateInfo?.candidate || ""; // Safely get candidate name
        const fileName = `${candidateName}_Learning_Attitude_Data.xlsx`;
    
        const worksheet = XLSX.utils.json_to_sheet(Learningattitude); // Convert data to worksheet
      //  console.log("this is the learning attitude",worksheet)
        const workbook = XLSX.utils.book_new(); // Create new workbook
      //  console.log("thisis the workboof learninr attitude",workbook)
        XLSX.utils.book_append_sheet(workbook, worksheet, "Learning Attitude"); // Add worksheet to workbook

        // Trigger file download
        XLSX.writeFile(workbook, fileName);
        if (chartRef.current) {
            html2canvas(chartRef.current).then((canvas) => {
                const imageUrl = canvas.toDataURL('image/png'); // Convert canvas to image URL
                const link = document.createElement('a');
                link.href = imageUrl;
                link.download = `${candidateName} Learning_Attitude.png`; // File name for the image
                link.click(); // Trigger the image download
            }).catch((error) => {
                console.error("Error exporting chart as image:", error);
            });
        } else {
            console.error("Chart element not found or not attached to the document!");
        }
    };

    const exportBudgetEstimate = () => {
        if (!candidateInfo || (Array.isArray(candidateInfo) && candidateInfo.length === 0)) {
            toast.warn(" Budget data is unavailable to export!");
            return;
        }
    
        const candidateName = candidateInfo?.candidate || ""; // Safely get candidate name
        const fileName = `${candidateName}_Budget_Estimate.xlsx`;
    
        // Ensure candidateInfo is an array
        let dataToExport = Array.isArray(candidateInfo) ? candidateInfo : [candidateInfo]; // Convert to array if it's not
    
        const worksheet = XLSX.utils.json_to_sheet(dataToExport); // Convert data to worksheet
    
        // Apply custom style to headers (bold and dark black background)
        const range = XLSX.utils.decode_range(worksheet['!ref']); // Get the range of the worksheet
        for (let col = range.s.c; col <= range.e.c; col++) {
            const headerCell = worksheet[XLSX.utils.encode_cell({r: 0, c: col})]; // Get the header cell
            if (headerCell) {
                headerCell.s = {
                    font: { bold: true, color: { rgb: "FFFFFF" } }, // Bold text and white font color
                    fill: { fgColor: { rgb: "000000" } }, // Dark black background
                    alignment: { horizontal: "center", vertical: "center" }, // Center the text
                };
            }
        }
    
        // Adjust row heights (optional) for better readability
        const rows = worksheet['!rows'];
        if (rows) {
            rows[0] = { hpt: 30 }; // Height of header row (optional)
        }
    
        const workbook = XLSX.utils.book_new(); // Create new workbook
        XLSX.utils.book_append_sheet(workbook, worksheet, "Candidate Info"); // Add worksheet to workbook
    
        // Trigger file download
        XLSX.writeFile(workbook, fileName);

        if (chartRef.current) {
            html2canvas(chartRef.current).then((canvas) => {
                const imageUrl = canvas.toDataURL('image/png'); // Convert canvas to image URL
                const link = document.createElement('a');
                link.href = imageUrl;
                link.download = `${candidateName}_Budget_Estimate.png`; // File name for the image
                link.click(); // Trigger the image download
            }).catch((error) => {
                console.error("Error exporting chart as image:", error);
            });
        } else {
            console.error("Chart element not found or not attached to the document!");
        }
    };
    
    const exportCareersPath = () => {
        if (!events || events.length === 0) {
            toast.warn(" Career Growth data unavailable to export!");
            return;
        }

        const candidateName = candidateInfo?.candidate || ""; // Safely get candidate name
        const fileName = `${candidateName}_Career_Growth.xlsx`;
    
        const worksheet = XLSX.utils.json_to_sheet(events); // Convert data to worksheet
      //  console.log("this is the learning attitude",worksheet)
        const workbook = XLSX.utils.book_new(); // Create new workbook
      //  console.log("thisis the workboof learninr attitude",workbook)
        XLSX.utils.book_append_sheet(workbook, worksheet, "Careers Growth"); // Add worksheet to workbook

        // Trigger file download
        XLSX.writeFile(workbook, fileName);
        if (chartRef.current) {
            html2canvas(chartRef.current).then((canvas) => {
                const imageUrl = canvas.toDataURL('image/png'); // Convert canvas to image URL
                const link = document.createElement('a');
                link.href = imageUrl;
                link.download = `${candidateName}_Career_Growth.png`; // File name for the image
                link.click(); // Trigger the image download
            }).catch((error) => {
                console.error("Error exporting chart as image:", error);
            });
        } else {
            console.error("Chart element not found or not attached to the document!");
        }
    };
    

    // Function to export Skills Data for Scatter Plot
    const exportSkillsData = () => {
        if (!skillsData || skillsData.length === 0) {
            toast.warn(" Skills Data is unavailable to export!");
            return;
        }
        const candidateName = candidateInfo?.candidate || ""; // Safely get candidate name
        const fileName = `${candidateName}Skills_Data.xlsx`;

        const worksheet = XLSX.utils.json_to_sheet(skillsData); // Convert data to worksheet
       // console.log("this is the skill data",worksheet)
        const workbook = XLSX.utils.book_new(); // Create new workbook
       // console.log("this ois skill data",workbook)
        XLSX.utils.book_append_sheet(workbook, worksheet, "Skills Data"); // Add worksheet to workbook

        // Trigger file download
        XLSX.writeFile(workbook, fileName);
        if (chartRef.current) {
            html2canvas(chartRef.current).then((canvas) => {
                const imageUrl = canvas.toDataURL('image/png'); // Convert canvas to image URL
                const link = document.createElement('a');
                link.href = imageUrl;
                link.download = `${candidateName}_Skills_Data.png`; // File name for the image
                link.click(); // Trigger the image download
            }).catch((error) => {
                console.error("Error exporting chart as image:", error);
            });
        } else {
            console.error("Chart element not found or not attached to the document!");
        }
    };
    const exportExpertiesLevel = () => {
        if (!categoriesCounts || categoriesCounts.length === 0) {
            toast.warn(" Expertise Level Data is unavailable for export!");
            return;
        }


        const candidateName = candidateInfo?.candidate || ""; // Safely get candidate name
        const fileName = `${candidateName}_Expertise_level.xlsx`;
    
        const worksheet = XLSX.utils.json_to_sheet(categoriesCounts); // Convert data to worksheet
      //  console.log("this is the learning attitude",worksheet)
        const workbook = XLSX.utils.book_new(); // Create new workbook
      //  console.log("thisis the workboof learninr attitude",workbook)
        XLSX.utils.book_append_sheet(workbook, worksheet, "Categories Counts"); // Add worksheet to workbook

        // Trigger file download
        XLSX.writeFile(workbook, fileName);
      //  const chartRef = document.getElementById('chart-container');  Get the chart container element (replace with your actual chart element's ID)
      if (chartRef.current) {
        html2canvas(chartRef.current).then((canvas) => {
            const imageUrl = canvas.toDataURL('image/png'); // Convert canvas to image URL
            const link = document.createElement('a');
            link.href = imageUrl;
            link.download = `${candidateName}_Expertise_Level_Chart.png`; // File name for the image
            link.click(); // Trigger the image download
        }).catch((error) => {
            console.error("Error exporting chart as image:", error);
        });
    } else {
        console.error("Chart element not found or not attached to the document!");
    }
    };
    useEffect(() => {
        // Debugging: Ensure the element is in the DOM and valid before the export function runs
        if (!chartRef.current) {
            console.log("Chart ref is not yet attached to the DOM");
        }
    }, [chartRef]);

    
    
    

    const [selectedDot, setSelectedDot] = useState(null);
    const handleDotClick = (e) => {
        setSelectedDot(e);
        console.log('Dot Clicked:', e);
    };

    const handlePointClick = (data) => {
        setModalContent(data);
        setModalIsOpen(true);
    };
    useEffect(() => {
        const filteredData = showData.filter((item) =>
            item.id.toString().includes(search)
        );
        if (search === "") {
            setData(showData);
            setClient("");
        } else {
            setData(filteredData);
        }
    }, [showData, search]);
    const [selectedGraph, setSelectedGraph] = useState("expertiseLevel");

    const handleGraphSelection = (graph) => {
        setSelectedGraph(graph);

    };
    const [isFormVisible, setIsFormVisible] = useState(true);
    const [cardHeight, setCardHeight] = useState("99%"); // Default height

    const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth <= 542);

    useEffect(() => {
        const handleResize = () => {
            setIsSmallScreen(window.innerWidth <= 542);
        };

        window.addEventListener("resize", handleResize);

        // Cleanup event listener on component unmount
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    return (
        <div className="wrapper">
            <LeftNav />
            <div className="section" style={{ backgroundColor: "#fff" }}>
                <TitleBar />

                <form
                    className="ss_formanal"
                    id="ss_formanal"
                    style={{ overflow: "hidden", maxHeight: "500px", maxWidth: "85%" }} // Adjust values as needed
                >
                    <div className="candianaly" style={{ maxHeight: "100%", maxWidth: "100%", justifyContent: "center" }}>
                        <div style={{ marginLeft: "-40px", zIndex: "4", maxWidth: "300px" }}>
                            <label className="analabel" htmlFor="report">Job Id</label>
                            <input
                                id="input_field"
                                onClick={() => setShowText(true)}
                                onChange={(e) => setSearch(e.target.value)}
                                value={search}
                                type="text"
                                className="input_fieldanal"
                                placeholder="Enter Job Id"
                                style={{ maxWidth: "280px" }} // Set input max-width
                            />
                            {showText && (
                                <div
                                    id="drop_down"
                                    className="drop_downanal"
                                    style={{
                                        maxHeight: "150px", // Limit dropdown height
                                        overflowY: "auto", // Add scroll if content overflows
                                        maxWidth: "350px",
                                        position: "absolute",
                                        zIndex: "11"
                                    }}
                                >
                                    {data?.map((item, idx) => (
                                        <div
                                            key={idx}
                                            style={{
                                                borderBottom: "1px solid #aaa",
                                                cursor: "pointer",
                                                padding: "5px",
                                                position: "relative",
                                                zIndex: "11"
                                            }}
                                            onClick={() => {
                                                setClient(item.client);
                                                setSearch(item.id.toString());
                                            }}
                                        >
                                            {item.id}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                        <div style={{ marginLeft: "10px", maxWidth: "300px" }}>
                            <label htmlFor="client">Client:</label>
                            <input
                                id="client"
                                className="clientanal"
                                type="text"
                                value={client}
                                readOnly
                                style={{ maxWidth: "280px" }}
                            />
                        </div>
                        <div style={{ marginLeft: "20px", maxWidth: "300px" }}>
                            <label htmlFor="resume" style={{ fontWeight: "bold" }}>Upload Resume:</label>
                            <input
                                style={{ maxWidth: "280px" }}
                                type="file"
                                name="resume"
                                id="resume"
                                className="resumeanal"
                                accept=".pdf,.doc,.docx"
                                onChange={handleResumeChange}
                                disabled={waitForSubmission}
                            />
                        </div>
                        <div className="buttonss" style={{ maxWidth: "200px" }}>
                            <button
                                type="button"
                                className="button_ss"
                                name="action"
                                value="Export to Excel"
                                onClick={handleSubmit}
                                style={{
                                    maxWidth: "120px",
                                }}
                                disabled={waitForSubmission}
                            >
                                {waitForSubmission ? "" : "Submit"}
                                <ThreeDots
                                    wrapperClass="ovalSpinner"
                                    wrapperStyle={{
                                        position: "absolute",
                                        top: "50%",
                                        left: "50%",
                                        transform: "translate(-50%, -50%)",
                                    }}
                                    visible={waitForSubmission}
                                    height="45"
                                    width="45"
                                    color="white"
                                    ariaLabel="oval-loading"
                                />
                            </button>
                        </div>
                    </div>
                </form>

                <div class="btnchart" style={{
                    marginTop: isFormVisible ? "0px" : "40px", // Apply margin-top conditionally
                    // Smooth transition for visual effect
                }}>
              
                    <button className={`barbtn ${selectedGraph === "expertiseLevel" ? 'selected' : ''}`} onClick={() => handleGraphSelection("expertiseLevel")} >
                    {isSmallScreen ? "" : "Expertise Level"}<FcBarChart />
                    </button>
                    <button className={`barbtn ${selectedGraph === "marketRelevance" ? 'selected' : ''}`} onClick={() => handleGraphSelection("marketRelevance")}>
                    {isSmallScreen ? "" : "Market Relevence"}  <FcBarChart />
                    </button >
                    <button className={`barbtn ${selectedGraph === "learningAttitude" ? 'selected' : ''}`} onClick={() => handleGraphSelection("learningAttitude")}>
                    {isSmallScreen ? "" :"Learning Attitude"}  <FcBarChart />
                    </button>
           
                   
                    <button className={`barbtn ${selectedGraph === "budgetEstimate" ? 'selected' : ''}`} onClick={() => handleGraphSelection("budgetEstimate")}>
                    {isSmallScreen ? "" :" Budget Estimate"}  <FcBarChart />
                    </button>
                    <button className={`barbtn ${selectedGraph === "careerGrowth" ? 'selected' : ''}`} onClick={() => handleGraphSelection("careerGrowth")}>
                    {isSmallScreen ? "" :"Career Growth"} <FcBarChart />
                    </button>
              
                    {/* <button class=" barbtn"  onClick={() => setIsFormVisible((prev) => !prev)} >
                  View Form
     <span style={{ fontSize: "15px", transform: isFormVisible ? "rotate(180deg)" : "rotate(0)",color:"skyblue" }}>
       &#9660;
     </span>
                 </button> */}
                </div>


                <div className="analy" style={{
                    marginBottom: "-13%"
                }}>


                    <div className="card_new" style={{
                        margin: selectedGraph === "careerGrowth" ? "10px 10px " : "10px", padding: selectedGraph === "careerGrowth" ? "0px 10px" : "10px", backgroundColor: "#ffffff", height: selectedGraph === "careerGrowth" ? "85%" : selectedGraph == "learningAttitude" ? "95%" : selectedGraph === "marketRelevance" ? "95%" : selectedGraph === "expertiseLevel" ? "95%" : "99%"
                        ,
                        marginBottom: selectedGraph === "careerGrowth" ? "0%" : "0",
                        overflowY: selectedGraph === "careerGrowth" ? "auto" : "hidden",
                        width: "75%"
                    }} >

                        {selectedGraph === "expertiseLevel" && (

                            <div >
                                <motion.div id='expertise' transition={{ duration: 0.7 }} className="car1"  >
                                    <div style={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        position: "sticky",
                                        top: "0",
                                        backgroundColor: "white",
                                        zIndex: "2",
                                        padding: "10px"
                                    }}>
                                        {/* Button aligned to the left */}
                                        <button
                                            onClick={   exportExpertiesLevel
                                            }
                                            style={{
                                                padding: "6px 15px",
                                                color: "#fff",
                                                backgroundColor: "#32406d",
                                                border: "none",
                                                borderRadius: "5px"
                                            }}
                                        >
                                            Export <FaFileExcel style={{ fontSize: "15px" }} />
                                        </button>

                                        {/* H5 aligned to the center */}
                                        <h5
                                            className="card-title"
                                            style={{
                                                color: "blue",
                                                fontSize: "20px",
                                                fontWeight: "500",
                                                textAlign: "center",
                                                flexGrow: 1,
                                                margin: "0"
                                            }}
                                        >
                                            Expertise Level
                                        </h5>
                                    </div>
                                    <div ref={chartRef}>
                                    <BargraphOV categoriesCounts={categoriesCounts} selectedReport="expertise_level" />
                                    </div>
                                </motion.div>
                            </div>

                        )}
                        {selectedGraph === "marketRelevance" && (
                            <motion.div whileHover={{ scale: 1.00 }} id='marketre' transition={{ duration: 0.7 }} className="car1">
                                <div style={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    position: "sticky",
                                    top: "0",
                                    backgroundColor: "white",
                                    zIndex: "2",
                                    padding: "10px"
                                }}>
                                    <button onClick={exportSkillsData} style={{
                                        padding: "6px 15px",
                                        color: "#fff",
                                        backgroundColor: "#32406d",
                                        border: "none",
                                        borderRadius: "5px"
                                    }}>
                                        Export  <FaFileExcel />
                                    </button>
                                    <h5 style={{
                                        color: "blue",
                                        fontSize: "20px",
                                        fontWeight: "500",
                                        textAlign: "center",
                                        flexGrow: 1,
                                        margin: "0"
                                    }}> Market Relevance </h5>
                                </div>
                                <div ref={chartRef}>
                                <ScatterPlot  skillsData={skillsData} selectedReport="market_relevence" />
                                </div>

                            </motion.div>
                        )}
                        {selectedGraph === "learningAttitude" && (
                            <motion.div whileHover={{ scale: 1.00 }} id="learningatti" style={{ paddingBottom: "10px", backgroundColor: "#fff", maxHeight: "450px",  height: window.innerWidth <= 370 ?'400px':"470px", marginBottom: "1%", overflow: "auto", }} transition={{ duration: 0.7 }} className="cad1">

                                <div style={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    position: "sticky",
                                    top: "0",
                                    backgroundColor: "white",
                                    zIndex: "2",
                                    padding: "10px"
                                }}>
                                    {/* Button aligned to the left */}
                                    <button
                                        onClick={exportLearningAttitude}
                                        style={{
                                            padding: "6px 15px",
                                            color: "#fff",
                                            backgroundColor: "#32406d",
                                            border: "none",
                                            borderRadius: "5px"
                                        }}
                                    >
                                        Export <FaFileExcel style={{ fontSize: "15px" }} />
                                    </button>

                                    {/* H5 aligned to the center */}
                                    <h5
                                        className="card-title"
                                        style={{
                                            color: "blue",
                                            fontSize: "20px",
                                            fontWeight: "500",
                                            textAlign: "center",
                                            flexGrow: 1,
                                            margin: "0"
                                        }}
                                    >
                                        Learning Attitude
                                    </h5>
                                </div>

                                <div ref={chartRef}>
                                    <Timelinechart Learningattitude={Learningattitude} selectedReport="learning_attitude" />


                                    {/* <div style={{ justifyContent: "center", marginTop: "-70px", marginLeft: "100px" }}>
                                    <table className="learningattitude">
                                        <tbody>
                                            <tr>
                                                <th style={{ textAlign: "left", }}>Company</th>
                                                <td style={{ paddingLeft: "5px",fontSize:"13px",fontWeight:"400" }}>{allCompanies.join(', ')}</td>
                                            </tr>
                                            <tr>
                                                <th style={{ textAlign: "left" }}>Overall Skills</th>
                                                <td style={{ paddingLeft: "10px",paddingLeft: "5px",fontSize:"13px",fontWeight:"400" }}>{allSkills.join(', ')}</td>
                                            </tr>
                                            <tr>
                                                <th style={{ textAlign: "left" }}>Overall Skills Count</th>
                                                <td style={{ paddingLeft: "10px",paddingLeft: "5px",fontSize:"13px",fontWeight:"400" }}>{allSkills.length}</td>
                                            </tr>
                                            <tr>
                                                <th style={{ textAlign: "left" }}>Certificates</th>
                                                <td style={{ paddingLeft: "10px",paddingLeft: "5px",fontSize:"13px",fontWeight:"400" }}>   {allCertificates.length > 0
                                                    ? allCertificates.join(', ')
                                                    : "Candidate doesn't have certificates"}</td>
                                            </tr>
                                        </tbody>
                                    </table>

                                </div> */}
                                </div>
                            </motion.div>
                        )}
                        {selectedGraph === "budgetEstimate" && (
                            < motion.div id='budgetest' style={{ overflowY: "auto",  height: window.innerWidth <= 370 ?'400px':"470px" }}>

                              
                                <div style={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        position: "sticky",
                                        top: "0",
                                        backgroundColor: "white",
                                        zIndex: "2",
                                        padding: "10px"
                                    }}>
                                        {/* Button aligned to the left */}
                                        <button
                                            onClick={exportBudgetEstimate}
                                            style={{
                                                padding: "6px 15px",
                                                color: "#fff",
                                                backgroundColor: "#32406d",
                                                border: "none",
                                                borderRadius: "5px"
                                            }}
                                        >
                                            Export <FaFileExcel style={{ fontSize: "15px" }} />
                                        </button>

                                        {/* H5 aligned to the center */}
                                        <h5
                                            className="card-title"
                                            style={{
                                                color: "blue",
                                                fontSize: "20px",
                                                fontWeight: "500",
                                                textAlign: "center",
                                                flexGrow: 1,
                                                margin: "0"
                                            }}
                                        >
                                           Budget Estimate
                                        </h5>
                                    </div>
                                <div >
                                    <div ref={chartRef}>
                                    <DualGaugeCharts minBudget={candidateInfo.minBudget} maxBudget={candidateInfo.maxBudget} candidate={candidateInfo.candidate} selectedReport="budget_estimate" />
                                    </div>
                                    <p style={{ color: "red" }}>{modalMessage}</p>

                                    <div style={{ display: "flex", justifyContent: "center", marginTop: "20px" }}>
                                        <table className="learningattitude" >
                                            <tbody>
                                                <tr>
                                                    <th style={{ textAlign: "left" }}>Candidate Name</th>
                                                    <td style={{ width: "200px", paddingLeft: "10px", fontSize: "13px", fontWeight: "400" }}>{candidateInfo.candidate}</td>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: "left" }}>
                                                        Candidate overall Experience</th>
                                                    <td style={{ paddingLeft: "10px" }}>{candidateInfo.CandidateExperience
                                                    }</td>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: "left" }}>
                                                        JD Experience(in Years)
                                                    </th>
                                                    <td style={{ paddingLeft: "10px", fontSize: "13px", fontWeight: "400" }}>   {candidateInfo.Jdexperience}</td>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: "left" }}>
                                                        Minumum Budget Package (LPA)
                                                    </th>
                                                    <td style={{ paddingLeft: "10px", fontSize: "13px", fontWeight: "400" }}>   {candidateInfo.JdMinBudget}</td>
                                                </tr>
                                                <tr>
                                                    <th style={{ textAlign: "left" }}>
                                                        Maximum Budget Package (LPA)
                                                    </th>
                                                    <td style={{ paddingLeft: "10px", fontSize: "13px", fontWeight: "400" }}>   {candidateInfo.JdMaxBudget}</td>
                                                </tr>



                                            </tbody>
                                        </table>

                                    </div>
                                </div>
                            </motion.div>
                        )}
                        {selectedGraph === "careerGrowth" && (
                            <motion.div id='careergrow' style={{ height: window.innerWidth <= 370 ?'400px':"700px", marginBottom: "-20%" }}  transition={{ duration: 0.7 }} className="cad1">

                              
                                <div style={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "center",
                                        position: "sticky",
                                        top: "0",
                                        backgroundColor: "white",
                                        zIndex: "2",
                                        padding: "10px"
                                    }}>
                                        {/* Button aligned to the left */}
                                        <button
                                            onClick={exportCareersPath}
                                            style={{
                                                padding: "6px 15px",
                                                color: "#fff",
                                                backgroundColor: "#32406d",
                                                border: "none",
                                                borderRadius: "5px"
                                            }}
                                        >
                                            Export <FaFileExcel style={{ fontSize: "15px" }} />
                                        </button>

                                        {/* H5 aligned to the center */}
                                        <h5
                                            className="card-title"
                                            style={{
                                                color: "blue",
                                                fontSize: "20px",
                                                fontWeight: "500",
                                                textAlign: "center",
                                                flexGrow: 1,
                                                margin: "0"
                                            }}
                                        >
                                        Career Growth
                                        </h5>
                                    </div>
                                    <div ref={chartRef}>
                                <Timeline events={events} selectedReport="career_growth" style={{ zIndex: "-3" }} />
                               </div>
                            </motion.div>
                        )}

                    </div>




                </div>
            </div>
            <Modal
                isOpen={showModal}
                onRequestClose={handleCloseModal}
                style={{
                    overlay: {
                        backgroundColor: "rgba(0, 0, 0, 0.5)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        zIndex: 1000,
                    },
                    content: {
                        top: "50%",
                        left: "50%",
                        right: "auto",
                        bottom: "auto",
                        marginRight: "-50%",
                        transform: "translate(-50%, -50%)",
                        height: "90vh",
                        width: activeCard === 'Career' ? "50%" : "70%",
                        overflow: "auto",
                        padding: "0px 20px",
                        border: "2px solid #32406D",
                        backgroundColor: "#fff",
                        boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.1)",
                        WebkitBackdropFilter: "blur(8px)",
                        backdropFilter: "blur(8px)",
                    },
                }}
            >
                <div>
                    {/* Flex Container for Buttons and Information */}
                    <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", padding: "10px", position: "sticky", top: "0", backgroundColor: "white", zIndex: "1", }}>
                        {/* Information Section */}
                        <div style={{ flex: 1, marginRight: "10px" }}>
                            <div style={{ top: "0", position: "sticky", backgroundColor: "white", zIndex: "1", padding: "15px" }}>
                                {activeCard === 'domainExpertise' && (
                                    <div>
                                        <div style={{ marginLeft: "40px" }}>
                                            <div><strong style={{ color: "green" }}>Category:</strong> {clickedData.category}</div>
                                            <div><strong style={{ color: "green" }}>Count:</strong> {clickedData.count}</div>
                                            <div><strong style={{ color: "green" }}>Items:</strong> {clickedData.items.join(', ')}</div>
                                        </div>
                                        <div >
                                            <h5 style={{ fontSize: "18px", color: "#32406D", marginLeft: "180px" }}>Expertise Level</h5>
                                        </div>
                                    </div>
                                )}
                                {activeCard === 'relevance' && (
                                    <div>
                                        <div style={{ marginLeft: "50px" }}>
                                            <div><strong style={{ color: "green" }}>Skill/Domain:</strong> {selectedDot ? selectedDot.payload.SkillDomain : 'N/A'}</div>
                                            <div><strong style={{ color: "green" }}>Relevance Score:</strong> {selectedDot ? selectedDot.payload.y : 0}</div>
                                            <div><strong style={{ color: "green" }}>Experience:</strong> {selectedDot ? selectedDot.payload.Experience : 'N/A'}</div>
                                        </div>
                                        <div>
                                            <h5 style={{ fontSize: "18px", color: "#32406D", marginLeft: "100px" }}> Market Relevance</h5>
                                        </div>
                                    </div>

                                )}
                                {activeCard === 'learning' && (
                                    <div>
                                        <div style={{ marginLeft: "50px" }}>
                                            <div><strong style={{ color: "green" }}>Company:</strong> {modalContent ? modalContent.name : "N/A"}</div>
                                            <div><strong style={{ color: "green" }}>Skill Count:</strong> {modalContent ? modalContent.skillCount : 0}</div>
                                            <div><strong style={{ color: "green" }}>Skills:</strong> {modalContent ? modalContent.skills : "N/A"}</div>
                                        </div>
                                        <div >
                                            <h5 style={{ fontSize: "18px", color: "#32406D", marginLeft: "180px" }}>Learning Attitude</h5>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                        {/* Buttons Section */}
                        <div style={{ display: "flex", flexDirection: "row", alignItems: "center", marginTop: activeCard === 'learning' ? "-100px" : (activeCard === 'relevance' ? "-100px" : (activeCard === 'domainExpertise' ? "-100px" : (activeCard === 'Career' ? "5px" : (activeCard === 'budget' ? "0px" : "-120px")))) }}>
                            <button
                                onClick={handleCloseModal}
                                style={{
                                    backgroundColor: "#ff6f6f",
                                    color: "#fff",
                                    border: "none",
                                    borderRadius: "5px",
                                    padding: "5px 10px",
                                    cursor: "pointer",
                                    fontSize: "16px",
                                }}
                            >
                                Close
                            </button>
                            {(activeCard === 'domainExpertise' || activeCard === 'learning' || activeCard === 'budget') && (
                                <button
                                    onClick={handleShowTable}
                                    style={{
                                        backgroundColor: "#007bff",
                                        color: "#fff",
                                        border: "none",
                                        borderRadius: "5px",
                                        padding: "5px 10px",
                                        cursor: "pointer",
                                        marginLeft: "10px",
                                        fontSize: "16px",
                                    }}
                                >
                                    {showTable ? "Hide Table" : "Show Table"}
                                </button>
                            )}
                        </div>

                    </div>
                    {/* {activeCard === 'domainExpertise' && (
                        <div style={{ position: "sticky", top: "120px", backgroundColor: "white", }}>
                            <h5 className="card-title" style={{ color: "#32406D", fontSize: "18px" }}>
                                Domain Expertise of the Candidate
                            </h5>
                        </div>
                    )} */}
                    {/* {activeCard === 'domainExpertise' && (
                        <div className="chart-container" style={{ width: "100%", height: '100%' }}>

                            <BargraphOV categoriesCounts={categoriesCounts} setClickedData={setClickedData} />
                            {showTable && activeCard === 'domainExpertise' && (
                                <div style={{ overflowX: 'auto', marginLeft: "50px" }}>
                                    <div className="bullet-points">
                                        <div style={{ marginTop: "10px" }}>
                                            <strong style={{ color: "green" }}>Bullet Points</strong>
                                        </div>

                                        <ul>
                                            {domains.map((domain, index) => (
                                                <li key={index}>
                                                    <strong>{domain.Domain}:</strong> {domain.description}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </div>

                            )}
                        </div>
                    )} */}
                    {/* {activeCard === 'learning' && (
                        <div style={{ position: "sticky", top: "120px", backgroundColor: "white", zIndex: "1", marginTop: "-20px" }}>
                            <h5 className="card-title" style={{ color: "#32406D", fontSize: "18px" }}>
                                Learning Attitude of the Candidate
                            </h5>
                        </div>
                    )} */}
                    {/* {activeCard === 'learning' &&
                        <div>
                            <Timelinechart Learningattitude={Learningattitude} onPointClick={handlePointClick} />
                            {showTable && activeCard === 'learning' && (
                                <div style={{ justifyContent: "center", marginTop: "-10px", marginLeft: "100px" }}>
                                    <table className="learningattitude">
                                        <tbody>
                                            <tr>
                                                <th style={{ textAlign: "left" }}>Company</th>
                                                <td style={{ paddingLeft: "10px" }}>{allCompanies.join(', ')}</td>
                                            </tr>
                                            <tr>
                                                <th style={{ textAlign: "left" }}>Overall Skills</th>
                                                <td style={{ paddingLeft: "10px" }}>{allSkills.join(', ')}</td>
                                            </tr>
                                            <tr>
                                                <th style={{ textAlign: "left" }}>Overall Skills Count</th>
                                                <td style={{ paddingLeft: "10px" }}>{allSkills.length}</td>
                                            </tr>
                                            <tr>
                                                <th style={{ textAlign: "left" }}>Certificates</th>
                                                <td style={{ paddingLeft: "10px" }}>   {allCertificates.length > 0
                                                    ? allCertificates.join(', ')
                                                    : "Candidate doesn't have certificates"}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div className="bullet-points">
                                        <div style={{ marginTop: "10px" }}>
                                            <strong style={{ color: "green" }}>Bullet Points :</strong>
                                        </div>
                                        <ul>
                                            {Object.entries(bulletPoints).map(([key, value]) => (
                                                <li key={key}>{value}</li>
                                            ))}
                                        </ul>
                                    </div>
                                    <div>
                                        <h3 style={{ color: "green" }}>Summary Paragraph:</h3>
                                        <p style={{ textAlign: "left" }}>{summaryParagraph}</p>
                                    </div>

                                </div>)}
                        </div>
                    }

                    {activeCard === 'budget' && (
                        <div style={{ position: "sticky", top: "50px", backgroundColor: "white", zIndex: "1", marginTop: "-20px" }}>
                            <h5 className="card-title" style={{ color: "#32406D", fontSize: "18px" }}>
                                Budget Estimate

                            </h5>
                        </div>
                    )}
                    {activeCard === 'budget' && (

                        <div style={{ marginTop: "20px" }}>

                            <DualGaugeCharts minBudget={candidateInfo.minBudget} maxBudget={candidateInfo.maxBudget} candidate={candidateInfo.candidate} isOpen={showModal} />
                            <div style={{ marginTop: "10px" }}>
                                <p style={{ color: "red" }}>{modalMessage}</p>
                            </div>
                            {showTable && activeCard === 'budget' && (
                                <div style={{ display: "flex", justifyContent: "center", marginTop: "20px" }}>
                                    <table className="learningattitude" >
                                        <tbody>
                                            <tr>
                                                <th style={{ textAlign: "left" }}>Candidate Name</th>
                                                <td style={{ width: "200px", paddingLeft: "10px" }}>{candidateInfo.candidate}</td>
                                            </tr>
                                            <tr>
                                                <th style={{ textAlign: "left" }}>
                                                    Candidate overall Experience</th>
                                                <td style={{ paddingLeft: "10px" }}>{candidateInfo.candidateExperience
                                                }</td>
                                            </tr>
                                            <tr>
                                                <th style={{ textAlign: "left" }}>
                                                    JD Experience(in Years)
                                                </th>
                                                <td style={{ paddingLeft: "10px" }}>   {candidateInfo.jdexperience}</td>
                                            </tr>

                                            <tr>
                                                <th style={{ textAlign: "left" }}>
                                                    Job Description Package (LPA)
                                                </th>
                                                <td style={{ paddingLeft: "10px" }}>   {candidateInfo.jdPackage}</td>
                                            </tr>


                                        </tbody>
                                    </table>

                                </div>)}
                        </div>
                    )}
                    {activeCard === 'Career' && (
                        <div style={{ position: "sticky", top: "58px", backgroundColor: "white", zIndex: "1", marginTop: "-20px" }}>
                            <h5 className="card-title" style={{ color: "#32406D", fontSize: "18px" }}>
                                Career Growth
                            </h5>
                        </div>
                    )}
                    {activeCard === 'Career' &&
                        <div style={{ marginTop: "25px" }}>
                            <Timeline events={events} />
                        </div>
                    } */}
                    {/* {activeCard === 'relevance' && (
                        <div style={{ position: "sticky", top: "100px", backgroundColor: "white", zIndex: "1", marginTop: "-20px" }}>
                            <h5 className="card-title" style={{ color: "#32406D", fontSize: "18px" }}>
                                Relevance of the Candidate Profile with Market Trends
                            </h5>
                        </div>
                    )} */}

                    {/* {activeCard === 'relevance' && (
                        <div className="ScatterPlots" >
                            <ScatterPlot skillsData={skillsData} width={1000} height={400} onDotClick={handleDotClick} />
                        </div>
                    )} */}
                </div>




            </Modal>
            <Modal
                isOpen={ExperienceshowModal}
                onRequestClose={handleCloseModal}
                contentLabel="Experience Confirmation"
                className="modal-content"
                overlayClassName="modal-overlay"
                style={{
                    overlay: {
                        backgroundColor: "rgba(0, 0, 0, 0.5)",
                        zIndex: 9999,
                        position: "fixed",
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                    },
                    content: {
                        width: "30%",
                        height: "100px",
                        margin: "auto",
                        flexDirection: "column",
                        justifyContent: "center",
                        alignItems: "center",
                        background: "#f7f7f7",
                        borderRadius: "10px",
                        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
                        padding: "20px 40px",
                        textAlign: "center",

                    },
                }}>
                <button onClick={handleCloseModal}>Close</button>
                <p>{modalMessage}</p>

            </Modal>
        </div>
    );
};

export default OverView;
