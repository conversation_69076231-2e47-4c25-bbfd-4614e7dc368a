import React, { useState, useEffect, useRef, uniRef } from "react";
import LeftNav from "../Components/LeftNav";
import TitleBar from "../Components/TitleBar";
import { MdFilterAlt } from "react-icons/md";
import {
  storedCandidates
} from "../Views/utilities";
import Modal from "react-modal";
import { IoMdSearch } from "react-icons/io";
import { MdOutlineYoutubeSearchedFor } from "react-icons/md";
import { PiMicrosoftExcelLogoFill } from "react-icons/pi";
import { FaFileDownload } from 'react-icons/fa';
import { MdCancel } from "react-icons/md";
import * as XLSX from "xlsx";
import { FcCheckmark } from "react-icons/fc";
import { TailSpin } from "react-loader-spinner";
import { RiMailCheckFill } from "react-icons/ri";
import { useDispatch } from 'react-redux';
import { setCandidates } from '../store/slices/candidateSlice'; // adjust path as needed
import { FcOk } from "react-icons/fc";
import "../Components/leftnav.css";
import "../Components/titlenav.css";
import "../Views/Dashboard/dashboard.css";
import "../Views/resumesearch.css";
import { toast } from "react-toastify";
import { Bounce } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { ToastContainer } from 'react-toastify';
//import 'react-toastify/dist/ReactToastify.css';
import { useSelector } from "react-redux";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Tooltip as ReactTooltip } from "react-tooltip";

import { FaAngleLeft } from "react-icons/fa6";
import { FaAngleRight } from "react-icons/fa6";
import { ThreeDots } from "react-loader-spinner";
import { Hourglass } from "react-loader-spinner";
import ResumeUpload from "../Components/ResumeUpload";
import { MdUploadFile } from "react-icons/md";
import JSZip from 'jszip';

//import { useSelector } from "react-redux";
import {
  faFileAlt,
} from "@fortawesome/free-solid-svg-icons";
// Note: zIndex is now available as a number constant in MUI v5
// You can use a direct number value instead, e.g., zIndex: 1000

function ResumeFetch() {
  const dispatch = useDispatch();
  const { dashboardData } = useSelector((state) => state.dashboardSliceReducer);
  const [ showItems, setShowItems ] = useState([]);
  const [ selectedProfile, setSelectedProfile ] = useState("");
  const [ searchTerm, setSearchTerm ] = useState("");
  const [ candidatesData, setCandidatesData ] = useState([]);
  // const [alldata, setAlldata] = useState([]);
  const [ isOpen, setIsOpen ] = useState(false);
  const [ waitForSubmission, setWaitForSubmission ] = useState(false);
  const [ showAllCandidates, setShowAllCandidates ] = useState(false);
  const [ loader, setLoader ] = useState(true);
  //page count for get all candidates
  const [ belowCount, setBelowCount ] = useState(0);
  // const [showItems, setShowItems] = useState([]);
  const [ countItems, setCountItems ] = useState(0);
  const [ id, setId ] = useState(1);
  const [ loading, setLoading ] = useState(true);

  // const [id, setId] = useState(1);

  // search all candidates
  const [ lowCount, setLowCount ] = useState(0); // Total candidates count

  const [ increasItems, setIncreasItems ] = useState(0); // Page count
  const [ currentPage, setCurrentPage ] = useState(1); // Current page
  // const [60, setItemsPerPage] = useState(60); // Candidates per page
  const [ selectedSkills, setSelectedSkills ] = useState([]);
  const [ selectedjobId, setSelectedjobId ] = useState([]);
  const [ selectedclient, setSelectedclient ] = useState([]);

  const notify = () => toast.success("Resume downloaded successfully!");
  const { jobs } = useSelector((state) => state.jobSliceReducer);
  const candidatesretrive = useSelector((state) => state.candidateSliceReducer.data);
  // const hasSubmitted = candidatesretrive.length > 0;
  // console.log(candidatesretrive, "candidates store in redux");
  const [ filteredRows, setFilteredRows ] = useState([]);
  // const profiles = jobs ? jobs.map((item) => item.role) : [];
  // const profiles = jobs ? jobs.map((item) => `${item.role} - ${item.client}`) : [];


  //  column Filtering

  const [ selectAllDate, setSelectAllDate ] = useState(false);
  const [ uniqueDataDate, setuniqueDataDate ] = useState([]);
  const [ dateSelected, setdateSelected ] = useState([]);

  const [ selectAll, setSelectAll ] = useState(false);
  const [ uniqueDataNames, setuniqueDataNames ] = useState([]);
  const [ nameSelected, setNameSelected ] = useState([]);

  const [ selectAllEmail, setSelectAllEmail ] = useState(false);
  const [ uniqueDataEmail, setuniqueDataEmail ] = useState([]);
  const [ emailSelected, setEmailSelected ] = useState([]);

  const [ selectAllMobile, setSelectAllMobile ] = useState(false);
  const [ uniqueDataMobile, setuniqueDataMobile ] = useState([]);
  const [ mobileSelected, setMobileSelected ] = useState([]);

  const [ selectedShares, setSelectedShares ] = useState([]);
  const [ selectAllClient, setSelectAllClient ] = useState(false);
  const [ uniqueDataClient, setuniqueDataClient ] = useState([]);
  const [ clientSelected, setclientSelected ] = useState([]);

  const [ selectAllProfile, setSelectAllProfile ] = useState(false);
  const [ uniqueDataProfile, setuniqueDataProfile ] = useState([]);
  const [ profileSelected, setprofileSelected ] = useState([]);

  const [ selectAllSkill, setSelectAllSkill ] = useState(false);
  const [ uniqueDataSkill, setuniqueDataSkill ] = useState([]);
  const [ skillSelected, setskillSelected ] = useState([]);

  const [ selectAllStatus, setSelectAllStatus ] = useState(false);
  const [ uniqueDataStatus, setuniqueDataStatus ] = useState([]);
  const [ statusSelected, setstatusSelected ] = useState([]);
  const [ showDetails, setShowDetails ] = useState(false);


  const [ isDateFiltered, setIsDateFiltered ] = useState(false);
  const [ isnameFiltered, setIsNameFiltered ] = useState(false);

  const [ isuseridFiltered, setIsUseridFiltered ] = useState(false);
  const [ ismobileFiltered, setIsMobileFiltered ] = useState(false);
  const [ isemailFiltered, setIsEmailFiltered ] = useState(false);
  const [ isclientFiltered, setIsClientFiltered ] = useState(false);
  const [ isprofileFiltered, setIsProfileFiltered ] = useState(false);
  const [ isskillFiltered, setIsSkillFiltered ] = useState(false);

  const [ isstatusFiltered, setIsStatusFiltered ] = useState(false);
  const [ isColorFiltered, setIsColorFiltered ] = useState(false);
  useEffect(() => {
    // console.log("firfilteredRows essst", filteredRows);
    const closeFilterPop = (e) => {
      const allRefIds = [
        "date_ref",
        "name_ref",
        "email_ref",
        "mobile_ref",
        "client_ref",
        "profile_ref",
        "skills_ref",
        "status_ref",

        "date_label_ref",
        "name_label_ref",
        "email_label_ref",
        "mobile_label_ref",
        "client_label_ref",
        "profile_label_ref",
        "skills_label_ref",

        "status_label_ref",
      ];
      let bool = false;
      for (const ref of allRefIds) {
        if (document.getElementById(ref)?.contains(e.target)) {
          bool = true;
          return;
        }
      }
      if (uniRef?.current?.contains(e.target) || bool) {
        // console.log("yes");
      } else {
        // console.log("no");
        setshowSearchjobassignment((prev) => ({
          ...Object.fromEntries(Object.keys(prev).map((key) => [ key, false ])),
        }));
      }
    };
    document.addEventListener("click", closeFilterPop);
    return () => {
      document.removeEventListener("click", closeFilterPop);
    };
  }, []);

  const [ showSearchjobassignment, setshowSearchjobassignment ] = useState({
    showSearchName: false,
    showSearchdate: false,
    // showSearchuserId: false,
    showSearchMobile: false,
    showSearchEmail: false,
    showSearchClient: false,
    showSearchProfile: false,
    showSearchSkill: false,
    showSearchStatus: false,
  });

  const handleOkClick = () => {
    // Reset both pagination states to ensure we always return to the first page
    // when filters are applied, regardless of which pagination mode is active
    setId(1);
    setCurrentPage(1);

    // Clear any selected items when applying filters
    setSelectItems([]);

    // Update filtered rows based on selected filters
    updateFilteredRows({
      dateSelected,
      nameSelected,
      emailSelected,
      mobileSelected,
      clientSelected,
      profileSelected,
      skillSelected,
      statusSelected,

      setuniqueDataDate,
      setuniqueDataNames,
      setuniqueDataEmail,
      setuniqueDataMobile,
      setuniqueDataClient,
      setuniqueDataProfile,
      setuniqueDataSkill,
      setuniqueDataStatus,
    });

    // Update filter indicator states
    setIsDateFiltered(dateSelected?.length > 0);
    setIsNameFiltered(nameSelected?.length > 0);
    setIsEmailFiltered(emailSelected?.length > 0);
    setIsMobileFiltered(mobileSelected?.length > 0);
    setIsClientFiltered(clientSelected?.length > 0);
    setIsProfileFiltered(profileSelected?.length > 0);
    setIsSkillFiltered(skillSelected?.length > 0);
    setIsStatusFiltered(statusSelected?.length > 0);

    // setshowSearchjobassignment((prev) =>
    //   Object.fromEntries(
    //     Object.entries(prev).map(([key, value]) => [key, false]),
    //   ),
    // );
  };

  useEffect(() => {
    // console.log("called handlokclick");
    handleOkClick();
  }, [
    dateSelected,
    nameSelected,

    emailSelected,
    mobileSelected,
    clientSelected,
    profileSelected,
    skillSelected,

    statusSelected,
  ]);

  const handleCheckboxChangeForDate = (date_created, event) => {
    // Stop event propagation to prevent the popup from closing
    if (event) {
      event.stopPropagation();
    }

    if (!date_created) return;

    const isSelected = dateSelected.includes(date_created.toString());
    if (isSelected) {
      setdateSelected((prev) => {
        return prev.filter((d) => d !== date_created.toString());
      });
      setSelectAllDate(false);
    } else {
      setdateSelected((prev) => {
        return [ ...prev, date_created.toString() ];
      });

      setSelectAllDate(dateSelected.length === uniqueDataDate.length - 1);
    }
  };

  const handleSelectAllForDate = (event) => {
    // Stop event propagation to prevent the popup from closing
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAllDate;
    setSelectAllDate(allChecked);

    if (allChecked) {
      setdateSelected(uniqueDataDate.map((d) => d.toString()));
    } else {
      setdateSelected([]);
    }
  };

  const handleCheckboxChange = (name, event) => {
    // Stop event propagation to prevent the popup from closing
    if (event) {
      event.stopPropagation();
    }

    const isSelected = nameSelected.includes(name);
    if (isSelected) {
      setNameSelected((prevSelected) =>
        prevSelected.filter((item) => item !== name),
      );
      setSelectAll(false);
    } else {
      setNameSelected((prevSelected) => [ ...prevSelected, name ]);
      setSelectAll(nameSelected.length === uniqueDataNames.length - 1);
    }
  };
  const handleSelectAllForName = (event) => {
    // Stop event propagation to prevent the popup from closing
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAll;
    setSelectAll(allChecked);

    if (allChecked) {
      setNameSelected(uniqueDataNames.map((d) => d?.toLowerCase()));
    } else {
      setNameSelected([]);
    }
  };
  const handleCheckboxChangeClient = (client, event) => {
    // Stop event propagation to prevent the popup from closing
    if (event) {
      event.stopPropagation();
    }

    if (!client) return;
    const isSelected = clientSelected.includes(client);
    if (isSelected) {
      setclientSelected((prevSelected) =>
        prevSelected.filter((item) => item !== client),
      );
      setSelectAllClient(false);
    } else {
      setclientSelected((prevSelected) => [ ...prevSelected, client ]);
      setSelectAllClient(clientSelected.length === uniqueDataClient.length - 1);
    }
  };
  const handleSelectAllForClient = (event) => {
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAllClient;
    setSelectAllClient(allChecked);

    if (allChecked) {
      setclientSelected(uniqueDataClient.map((d) => d?.toLowerCase()));
    } else {
      setclientSelected([]);
    }
  };

  const handleCheckboxChangeProfile = (profile, event) => {
    if (event) {
      event.stopPropagation();
    }
    if (!profile) return;
    const isSelected = profileSelected.includes(profile);
    if (isSelected) {
      setprofileSelected((prevSelected) =>
        prevSelected.filter((item) => item !== profile),
      );
      setSelectAllProfile(false);
    } else {
      setprofileSelected((prevSelected) => [ ...prevSelected, profile ]);
      setSelectAllProfile(
        profileSelected.length === uniqueDataProfile.length - 1,
      );
    }
  };
  const handleSelectAllForProfile = (event) => {
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAllProfile;
    setSelectAllProfile(allChecked);

    if (allChecked) {
      setprofileSelected(uniqueDataProfile.map((d) => d?.toLowerCase()));
    } else {
      setprofileSelected([]);
    }
  };

  const handleCheckboxChangeSkill = (skills, event) => {
    if (event) {
      event.stopPropagation();
    }

    const isSelected = skillSelected.includes(skills);
    if (isSelected) {
      setskillSelected((prevSelected) =>
        prevSelected.filter((item) => item !== skills),
      );
      setSelectAllSkill(false);
    } else {
      setskillSelected((prevSelected) => [ ...prevSelected, skills ]);
      setSelectAllSkill(skillSelected.length === uniqueDataSkill.length - 1);
    }
  };
  const handleSelectAllForSkill = (event) => {
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAllSkill;
    setSelectAllSkill(allChecked);

    if (allChecked) {
      setskillSelected(uniqueDataSkill.map((d) => d?.toLowerCase()));
    } else {
      setskillSelected([]);
    }
  };
  const handleCheckboxChangeStatus = (status, event) => {
    if (event) {
      event.stopPropagation();
    }

    const isSelected = statusSelected.includes(status);
    if (isSelected) {
      setstatusSelected((prevSelected) =>
        prevSelected.filter((item) => item !== status),
      );
      setSelectAllStatus(false);
    } else {
      setstatusSelected((prevSelected) => [ ...prevSelected, status ]);
      setSelectAllStatus(statusSelected.length === uniqueDataStatus.length - 1);
    }
  };
  const handleSelectAllForStatus = (event) => {
    if (event) {
      event.stopPropagation();
    }

    const allChecked = !selectAllStatus;
    setSelectAllStatus(allChecked);

    if (allChecked) {
      setstatusSelected(uniqueDataStatus.map((d) => d?.toLowerCase()));
    } else {
      setstatusSelected([]);
    }
  };
  // email handle ----------------------------------------------------------------------------------
  const handleCheckboxChangeEmail = (email) => {
    const isSelected = emailSelected.includes(email);
    if (isSelected) {
      setEmailSelected((prevSelected) =>
        prevSelected.filter((item) => item !== email),
      );
      setSelectAllEmail(false);
    } else {
      setEmailSelected((prevSelected) => [ ...prevSelected, email ]);
      setSelectAllEmail(emailSelected.length === uniqueDataEmail.length - 1);
    }
  };

  const handleSelectAllForEmail = () => {
    const allChecked = !selectAllEmail;
    setSelectAllEmail(allChecked);

    if (allChecked) {
      setEmailSelected(uniqueDataEmail.map((d) => d?.toLowerCase()));
    } else {
      setEmailSelected([]);
    }
  };

  const handleCheckBoxChangeForMobile = (mobile) => {
    const isSelected = mobileSelected.includes(mobile);
    if (isSelected) {
      setMobileSelected((prevSelected) =>
        prevSelected.filter((item) => item !== mobile),
      );
      setSelectAllMobile(false);
    } else {
      setMobileSelected((prevSelected) => [ ...prevSelected, mobile ]);
      setSelectAllMobile(mobileSelected.length === uniqueDataMobile.length - 1);
    }
  };

  const handleSelectAllForMobile = () => {
    const allChecked = !selectAllMobile;
    setSelectAllMobile(allChecked);

    if (allChecked) {
      setMobileSelected(uniqueDataMobile.map((d) => d.toString()));
    } else {
      setMobileSelected([]);
    }
  };
  //   filter of column

  // Removed unused state variable
  const updateFilteredRows = ({
    dateSelected,
    nameSelected,

    mobileSelected,
    emailSelected,
    clientSelected,
    profileSelected,
    skillSelected,

    statusSelected,

    setuniqueDataDate,
    setuniqueDataNames,
    setuniqueDataMobile,
    setuniqueDataEmail,
    setuniqueDataClient,
    setuniqueDataProfile,
    setuniqueDataSkill,
    setuniqueDataStatus,
  }) => {

    let prevfilteredRows = candidatesData;
    if (dateSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        dateSelected?.includes(row.date_created.toString()),
      );
      // console.log("ifff", dateSelected);
    }

    if (nameSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        nameSelected?.includes(row.name?.toLowerCase()),
      );
    }
    if (emailSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        emailSelected.includes(row.email?.toLowerCase()),
      );
    }
    if (mobileSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        mobileSelected.includes(row.mobile.toString()),
      );
    }
    if (clientSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        clientSelected.includes(row.client?.toLowerCase()),
      );
    }
    if (profileSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        profileSelected.includes(row.profile?.toLowerCase()),
      );
    }
    if (skillSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        skillSelected.includes(row.skills?.toLowerCase()),
      );
    }

    if (statusSelected?.length !== 0) {
      prevfilteredRows = prevfilteredRows.filter((row) =>
        statusSelected.includes(row.status?.toLowerCase()),
      );
    }

    const arrayNames = [
      "dateSelected",
      "nameSelected",

      "emailSelected",
      "mobileSelected",
      "clientSelected",
      "profileSelected",
      "skillSelected",

      "statusSelected",
    ];

    const arrays = [
      dateSelected,
      nameSelected,

      emailSelected,
      mobileSelected,
      clientSelected,
      profileSelected,
      skillSelected,

      statusSelected,
    ];

    // Create an array of filter names that have active selections
    let NamesOfNonEmptyArray = [];
    arrays.forEach((arr, index) => {
      if (arr?.length > 0) {
        NamesOfNonEmptyArray.push(arrayNames[ index ]);
      }
    });
    if (!NamesOfNonEmptyArray.includes("dateSelected")) {
      setuniqueDataDate(() => {
        // console.log(
        //   "first",
        //   Array.from(
        //     new Set(
        //       prevfilteredRows.map((filteredRow) => {
        //         return "date1";
        //       }),
        //     ),
        //   ),
        // );
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.date_created;
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("nameSelected")) {
      setuniqueDataNames(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.name;
            }),
          ),
        );
      });
    }

    if (!NamesOfNonEmptyArray.includes("emailSelected")) {
      setuniqueDataEmail(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.email?.trim();
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("mobileSelected")) {
      setuniqueDataMobile(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.mobile;
            }),
          ),
        );
      });
    }

    if (!NamesOfNonEmptyArray.includes("clientSelected")) {
      setuniqueDataClient(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.client?.trim();
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("profileSelected")) {
      setuniqueDataProfile(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.profile?.trim();
            }),
          ),
        );
      });
    }
    if (!NamesOfNonEmptyArray.includes("skillSelected")) {
      setuniqueDataSkill(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.skills?.trim();
            }),
          ),
        );
      });
    }

    if (!NamesOfNonEmptyArray.includes("statusSelected")) {
      setuniqueDataStatus(() => {
        return Array.from(
          new Set(
            prevfilteredRows.map((filteredRow) => {
              return filteredRow.status;
            }),
          ),
        );
      });
    }
    // Update the filtered rows state
    setFilteredRows(prevfilteredRows);

    // Update the count states for pagination
    setLowCount(prevfilteredRows.length);
    setBelowCount(prevfilteredRows.length);

    // Update the number of pages based on the filtered results
    setIncreasItems(Math.ceil(prevfilteredRows.length / 60));
  };


  useEffect(() => {
    // Initialize empty arrays for all selected filters
    setdateSelected([]);
    setNameSelected([]);
    setEmailSelected([]);
    setMobileSelected([]);
    setclientSelected([]);
    setprofileSelected([]);
    setskillSelected([]);
    setstatusSelected([]);

    updateFilteredRows({
      dateSelected: [],
      nameSelected: [],
      emailSelected: [],
      mobileSelected: [],
      clientSelected: [],
      profileSelected: [],
      skillSelected: [],
      statusSelected: [],

      setuniqueDataDate,
      setuniqueDataNames,
      setuniqueDataMobile,
      setuniqueDataEmail,
      setuniqueDataClient,
      setuniqueDataProfile,
      setuniqueDataSkill,
      setuniqueDataStatus,
    });
  }, []);
  const profiles = jobs
    ? jobs.map((item) => ({
      name: `${item.role} - ${item.client}`,
      roleid: item.id,
      client: item.client,
      role: item.role,
      JobIds: item.id,
      Jobskill: item.skills,
    }))
    : [];


  const filteredProfiles = profiles.filter((profile) =>
    profile.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
  // console.log(filteredProfiles.length, "length")

  // useEffect(() => {
  //   if (selectedProfile) localStorage.setItem('selectedProfile', selectedProfile);
  // }, [selectedProfile]);

  // useEffect(() => {
  //   if (selectedSkills) localStorage.setItem('selectedSkills', JSON.stringify(selectedSkills));
  // }, [selectedSkills]);

  // useEffect(() => {
  //   if (selectedclient) localStorage.setItem('selectedclient', selectedclient);
  // }, [selectedclient]);

  // useEffect(() => {
  //   if (selectedjobId) localStorage.setItem('selectedjobId', selectedjobId);
  // }, [selectedjobId]);

  // useEffect(() => {
  //   if (searchTerm) localStorage.setItem('searchTerm', searchTerm);
  // }, [searchTerm]);


  const handleProfileChange = (item) => {
    console.log(item, "selectedjodddddddddddddddddddddddddbid")
    // console.log(roleid,"selectroleids")
    setIsOpen(false);
    // const Jobclient = matchedJobs.map(job => job.client);
    setSelectedProfile(item.role);
    setSearchTerm(item.role);
    setSelectedclient(item.client);

    setSelectedjobId(item.roleid);
    const skillsArray = typeof item.Jobskill === 'string'
      ? item.Jobskill.split(',').map(skill => skill.trim())
      : item.Jobskill;
    console.log(skillsArray, " set skils in localstorage")
    setSelectedSkills(skillsArray);

    localStorage.setItem('selectedProfile', item.role);
    localStorage.setItem('selectedjobId', item.roleid);
    localStorage.setItem('selectedSkills', JSON.stringify(skillsArray));
  };

  console.log(selectedjobId, "selectedjobid")
  useEffect(() => {
    const storedProfile = localStorage.getItem('selectedProfile');
    const storedjobId = localStorage.getItem('selectedjobId');
    const storedSkills = JSON.parse(localStorage.getItem('selectedSkills') || '[]');
    // const storedClient = localStorage.getItem('selectedclient');
    // const storedJobId = localStorage.getItem('selectedjobId');
    console.log(storedSkills, "storedSkills");
    const storedSearchTerm = localStorage.getItem('searchTerm');

    if (storedProfile) setSelectedProfile(storedProfile);
    if (storedjobId) setSelectedjobId(storedjobId);
    // if (storedProfile) setSearchTerm(storedProfile);
    if (storedSkills.length > 0) setSelectedSkills(storedSkills);
    // if (storedClient) setSelectedclient(storedClient);
    // if (storedJobId) setSelectedjobId(storedJobId);
    if (storedProfile) setSearchTerm(storedProfile);
  }, []);

  const { StoredCandidateData } = useSelector((state) => state.storeCandidateSliceReducer);
  //   console.log(StoredCandidateData, "cxadd")
  // console.log(storedCandidates(),"cxadd")

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setIsOpen(true);
    if (e.target.value === "") {
      setShowAllCandidates(true);
    }


  };


  const [ hasSubmitted, setHasSubmitted ] = useState(false);
  const [ showImageModal, setShowImageModal ] = useState(false);
  const closeModals = () => {
    setShowImageModal(false);  // Close modal
  };
  const [ warningMessage, setWarningMessage ] = useState('');
  const handleSubmit = async () => {
    if (!waitForSubmission) {
      setWaitForSubmission(true);
      setLoading(true);
      setHasSubmitted(true);

      // Reset pagination to first page whenever a new search is performed
      setCurrentPage(1);
      setId(1);

      if (!searchTerm) {
        // If searchTerm is empty, show all candidates
        toast.error("Please enter a job title to search.");
        setLoading(false);
        setShowAllCandidates(true);
        setWaitForSubmission(false);
        return;
      }

      if (!selectedSkills) {
        toast.error("Please enter at least one skill.");
        setLoading(false);
        setWaitForSubmission(false);
        return;
      }

      if (selectedProfile) {
        const skillsString = selectedSkills;
        try {
          const response = await fetch("http://192.168.0.47:5002/match-candidates", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ job_role: selectedProfile, skills: skillsString }),
          });

          if (response.ok) {
            const data = await response.json();
            toast.success(data.message);
            const matchedCandidates = data.candidates;

            const filteredCandidates = matchedCandidates.filter(candidate => candidate.client !== selectedclient);
            const excludedCandidates = matchedCandidates.filter(candidate => candidate.client === selectedclient);
            if (filteredCandidates.length > 0) {
              dispatch(setCandidates(filteredCandidates));
              setSelectItems([]);
            } else {
              toast.info("No candidates found matching your search criteria.");
            }
          } else {
            const errorData = await response.json();
            toast.error(errorData.message);
          }
        } catch (error) {
          console.error("Error submitting profile:", error);
          toast.error("An error occurred while searching for candidates.");
        } finally {
          setWaitForSubmission(false);
          setLoading(false);
        }
      }
    }
  };

  useEffect(() => {

    if (candidatesretrive && candidatesretrive.length > 0) {
      setHasSubmitted(true); // Persist submission state if Redux has data
      setLoading(false)
    }
    setCandidatesData(candidatesretrive); // Assign fetched data to state
    setLowCount(candidatesretrive.length);
    setIncreasItems(Math.ceil(candidatesretrive.length / 60));
    setShowAllCandidates(false);

    setBelowCount(candidatesretrive.length);

    setuniqueDataDate([
      ...new Set(
        candidatesretrive.map((d) =>
          new Date(d.date_created).toISOString().split('T')[ 0 ]
        )
      ),
    ]);
    setuniqueDataNames([ ...new Set(candidatesretrive.map((d) => d.name)) ]);
    setuniqueDataEmail([ ...new Set(candidatesretrive.map((d) => d.email)) ]);
    setuniqueDataMobile([ ...new Set(candidatesretrive.map((d) => d.mobile)) ]);
    setuniqueDataClient([ ...new Set(candidatesretrive.map((d) => d.client)) ]);
    setuniqueDataProfile([ ...new Set(candidatesretrive.map((d) => d.profile)) ]);
    setuniqueDataSkill([ ...new Set(candidatesretrive.map((d) => d.skills)) ]);
    setuniqueDataStatus([ ...new Set(candidatesretrive.map((d) => d.status)) ]);

    setFilteredRows(candidatesretrive);
  }, [ candidatesretrive ]); // ✅ Add dependencies here

  useEffect(() => {
    const handleClick = (e) => {
      const target = e.target;
      // console.log(target);
      // console.log("click detected");
      // console.log(allJobs);

      const idx = candidatesData.findIndex((item) => {
        return (
          item.id.toString() === target.id.substring(0, target.id.length - 1)
        );
      });
      // console.log(idx);

      if (idx !== -1) {
        console.log(candidatesData[ idx ]);

        // Update the state of showItems based on the clicked target
        const update = new Array(candidatesData.length)
          .fill()
          .map((_, index) => ({
            id: candidatesData[ index ].id.toString(),
            client: false,
            recruiter: false,
            role: false,
          }));

        if (target.id.endsWith("1")) {
          update[ idx ] = {
            ...showItems[ idx ],
            client: false,
            skills: !showItems[ idx ]?.skills,
            role: false,
          };
        }
        console.log(update);
        setShowItems(update);
      } else {
        if (

          target.id === "default1"
        )
          return;

        const initial = new Array(candidatesData.length).fill().map((_, index) => ({
          id: candidatesData[ index ].id.toString(),
          client: false,
          recruiter: false,
          role: false,
        }));
        setShowItems(initial);
        // console.log("outside");
      }
    };

    window.addEventListener("click", handleClick);
    return () => {
      window.removeEventListener("click", handleClick);
    };
  }, [ candidatesData, showItems ]);
  //  const  handelgetalldata = () =>{
  //   storedCandidates();
  //   setShowAllCandidates(true)
  //   }
  const resumeApiCall = async (candidate) => {
    try {
      const response = await fetch(
        `http://192.168.0.47:5002/view_resume/${candidate.id}`,
        {
          method: "GET",
        }
      );

      if (response.ok) {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);

        // Open the resume in a new tab for viewing
        window.open(url, "_blank");

        // Optional: Cleanup after some time
        setTimeout(() => URL.revokeObjectURL(url), 10000); // Revoke after 10 seconds
      } else {
        console.log("Failed to fetch resume:", response.statusText);
      }
    } catch (err) {
      console.log("Error fetching resume:", err);
    }
  };


  // page number

  const getPageRange = () => {
    const pageRange = [];
    const maxPagesToShow = 5; // Adjust this value to show more or fewer page numbers

    let startPage = Math.max(1, id - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(countItems, startPage + maxPagesToShow - 1);

    if (endPage - startPage < maxPagesToShow - 1) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    if (startPage > 1) {
      pageRange.push(1);
      if (startPage > 2) {
        pageRange.push("...");
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pageRange.push(i);
    }

    if (endPage < countItems) {
      if (endPage < countItems - 1) {
        pageRange.push("...");
      }
      pageRange.push(countItems);
    }

    return pageRange;
  };
  //console.log(countItems,"countsnumber")
  const goToPage = (pageNumber) => {
    // Only process numeric page numbers (skip "..." in pagination)
    if (typeof pageNumber === "number" && pageNumber >= 1 && pageNumber <= countItems) {
      setId(pageNumber);
      // Clear any selected items when changing pages to avoid confusion
      setSelectItems([]);
    }
  };
  // search candidates page count

  const getPageRangesearch = () => {
    const pageRange = [];
    const maxPagesToShow = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(increasItems, startPage + maxPagesToShow - 1);

    if (endPage - startPage < maxPagesToShow - 1) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    if (startPage > 1) {
      pageRange.push(1);
      if (startPage > 2) {
        pageRange.push("...");
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pageRange.push(i);
    }

    if (endPage < increasItems) {
      if (endPage < increasItems - 1) {
        pageRange.push("...");
      }
      pageRange.push(increasItems);
    }

    return pageRange;
  };

  const goToPagesearch = (pageNumber) => {
    // Only process numeric page numbers (skip "..." in pagination)
    if (typeof pageNumber === "number" && pageNumber >= 1 && pageNumber <= increasItems) {
      setCurrentPage(pageNumber);
      // Clear any selected items when changing pages to avoid confusion
      setSelectItems([]);
    }
  };



  const [ list, setList ] = useState([]);



  const [ ResumeModal, setResumeModal ] = useState(false);




  const [ isAnimating, setIsAnimating ] = useState(false);


  useEffect(() => {
    setIsAnimating(true);
    setTimeout(() => {
      setIsAnimating(false);
    }, 500); // Adjust the timeout to match your animation duration
  }, [ location ]);

  const [ waitForSubmissionemail, setWaitForSubmissionemail ] = useState(false);
  const [ selectItems, setSelectItems ] = useState([]);
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      // Get only the candidates from the current page based on which pagination mode is active
      let pageCandidates;
      if (showAllCandidates) {
        // Using id for pagination
        pageCandidates = filteredRows.slice((id - 1) * 60, id * 60);
      } else {
        // Using currentPage for pagination
        pageCandidates = filteredRows.slice((currentPage - 1) * 60, currentPage * 60);
      }
      setSelectItems(pageCandidates);
    } else {
      setSelectItems([]);
    }
  };


  const handleSelectIndividual = (id, e) => {
    if (e.target.checked) {
      const selectedCandidate = candidatesData.find(item => item.id === id);
      setSelectItems(prev => [ ...prev, selectedCandidate ]);
    } else {
      setSelectItems(prev => prev.filter(item => item.id !== id));
    }
  };

  const recruitername = localStorage.getItem("name");
  const job_role = searchTerm

  const [ emailSubject, setEmailSubject ] = useState();
  useEffect(() => {
    if (searchTerm) {
      setEmailSubject(`Exciting Opportunity for the Role of ${searchTerm} `);
    }
  }, [ searchTerm ]);


  const bodyRef = useRef(null);
  const getEmailBodyContent = () => {
    return bodyRef.current?.innerHTML || "";
  };
  // const placeholderRegex = /\{[a-zA-Z0-9_]+\}/g;
  const [ showEmailModal, setShowEmailModal ] = useState(false);

  const closeModal = () => {
    setShowEmailModal(false);

  };
  useEffect(() => {
    const handleKeyDown = (e) => {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;

      const range = selection.getRangeAt(0);
      const { startContainer, startOffset } = range;

      // Get the node before the cursor
      const prevNode =
        startOffset > 0
          ? startContainer.childNodes[ startOffset - 1 ] || startContainer
          : startContainer.previousSibling;

      const nextNode = startContainer.childNodes[ startOffset ];

      const isDeletingPlaceholder =
        (e.key === 'Backspace' && prevNode?.contentEditable === 'false') ||
        (e.key === 'Delete' && nextNode?.contentEditable === 'false');

      if (isDeletingPlaceholder) {
        e.preventDefault();
      }
    };

    const current = bodyRef.current;
    if (current) {
      current.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      if (current) {
        current.removeEventListener('keydown', handleKeyDown);
      }
    };
  }, []);


  const user_type = localStorage.getItem("user_type");
  const handleEmailSend = async () => {
    if (!waitForSubmissionemail) {
      setWaitForSubmissionemail(true);
      //    console.log(searchTerm,"searchedvalue")
      if (selectItems.length === 0) {
        toast.warn("Please select at least one candidate.");
        return;
      }
      const candidates = selectItems.map(({ resume_base64, date_created, match_score, status, reason, matching_percentage, mobile, id, client, skills, ...rest }) => rest);
      const content = getEmailBodyContent();

      if (selectItems) {
        try {
          const response = await fetch("http://192.168.0.47:5002/snd_emails", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              candidates, job_role, recruitername, job_id: selectedjobId, subject: emailSubject,
              body: content,
            }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();

          if (response.ok) {

            setShowEmailModal(false);
            setWaitForSubmissionemail(false);
            toast.success(data.message);
            // toast.success("Mail sended respect to that candidate successfully");
            setSelectItems([]);
            // setSearchTerm('')
            // setEmailBody('')
            // setSelectedSkills('')
          } else {
            toast.error(data.message);
            setShowEmailModal(false);
            setWaitForSubmissionemail(false);
          }
          // fetchAllCandidates()

        } catch (error) {
          console.error("Error submitting profile:", error);
          setWaitForSubmissionemail(false);
        }
      }
    }
  };


  const handledownloadResumes = async () => {
    if (selectItems.length === 0) {
      toast.warn("Please select at least one candidate to download resumes.");
      return;
    }
    const candidateIds = selectItems.map(item => item.id);
    try {
      const response = await fetch(

        "http://192.168.0.47:5002/download_resumess",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ candidate_ids: candidateIds }),
        },
      );
      if (response.ok) {

        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        window.open(url, "_blank");
        // toast.success("Resumes downloaded successfully!");
        // setSelectItems('')
        setSelectItems([]);
      } else {
        console.log("Failed to fetch jd_file:", response.statusText);
      }
    } catch (err) {
      console.log("Error fetching jd_file:", err);
    }
  };


  const statusColorMapping = {
    "selected": "green",
    "boarded": "green",
    "rejected": "red",
    "hold": "red",
    "drop": "red",
    "duplicate": "red",
    "show": "red",
  };


  const getStatusColor = (status) => {
    const statusLowerCase = status.toLowerCase();
    if (statusColorMapping.hasOwnProperty(statusLowerCase)) {
      return statusColorMapping[ statusLowerCase ];
    }
    if (statusLowerCase.includes("selected") ||
      statusLowerCase.includes("boarded")) {
      return "green";
    } else if (
      statusLowerCase.includes("rejected") ||
      statusLowerCase.includes("hold") ||
      statusLowerCase.includes("drop") ||
      statusLowerCase.includes("duplicate") ||
      statusLowerCase.includes("show")
    ) {
      return "red";
    } else {
      return "orange";
    }
  };

  // useEffect(() => {
  //   const handleClickOutside = (event) => {
  //     if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
  //       setIsOpen(false);
  //     }
  //   };

  //   document.addEventListener("mousedown", handleClickOutside);

  //   return () => {
  //     document.removeEventListener("mousedown", handleClickOutside);
  //   };
  // }, []);
  // console.log(candidatesData, "candidatesData");
  // console.log(filteredRows, "filteredRows");

  //  console.log(filteredRows,"fetched then recruittteteve")

  return (

    <div className="wrapper">
      <LeftNav />
      <div className="section">
        <TitleBar />

        <div
          className="mobiledash useraccco"
          style={{ gap: "10px" }}
        >
          <label
            style={{
              marginTop: "1vh",
              fontWeight: "500",
              paddingRight: "5px",
            }}
          >
            {/* search */}
          </label>


          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            flexWrap: 'wrap',
            zIndex: 2
          }}>
            {/* Header */}
            <h5
              id="theader"
              className="joblisthed"
              style={{
                fontSize: "18px",
                fontWeight: "700",
                marginRight: "auto"
              }}
            >
              Resume Portal
            </h5>

            {/* Download Resumes */}
            <button
              onClick={handledownloadResumes}
              style={{
                display: 'flex',
                position: 'relative',
                padding: '3px',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: "5px",
                border: "none",
                height: '30px',
                backgroundColor: '#f1f1f1'
              }}
            >
              {selectItems.length > 0 && (
                <span style={{
                  position: 'absolute',
                  top: '-5px',
                  right: '-5px',
                  backgroundColor: 'red',
                  color: 'white',
                  fontSize: '10px',
                  padding: '2px 6px',
                  borderRadius: '50%',
                  zIndex: 1,
                  fontWeight: 'bold'
                }}>
                  {selectItems.length}
                </span>
              )}
              <FaFileDownload
                style={{ fontSize: "20px", color: "#32406d" }}
                data-tooltip-id={"remove_search"}
                data-tooltip-content="Resumes Download"
              />
              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="Resume Upload"
              />
            </button>

            {/* Send Email */}
            <div
              className="remove_filter_icons"
              onClick={() => {
                if (selectItems.length > 0) {
                  setShowEmailModal(true);
                }
              }}
              style={{
                display: 'flex',
                position: 'relative',
                padding: '3px',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: "5px",
                height: '30px',
                backgroundColor: '#f1f1f1',
                // cursor: 'pointer'
                //  cursor: !searchTerm.trim() ? 'not-allowed' : 'pointer'
                cursor: selectItems.length === 0 ? 'not-allowed' : 'pointer',
              }}
              disabled={selectItems.length === 0}
            >


              <RiMailCheckFill
                style={{ fontSize: "25px", color: "#32406d" }}
                data-tooltip-id="remove_search"
                data-tooltip-content="Send Mail"
              />

              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="Resume Upload"
              />
            </div>

            {/* Search Input */}
            <div style={{ position: 'relative' }}>
              <IoMdSearch
                style={{ position: 'absolute', top: '4px', left: '8px', height: "22px", width: "22px", color: "#32406d" }}
              />
              <input
                placeholder="Select a profile..."
                className="searching"
                style={{
                  height: "30px",
                  width: "280px",
                  paddingLeft: "35px",
                  borderRadius: "5px",
                  border: "1px solid #ccc"
                }}
                value={searchTerm}
                onChange={handleSearchChange}
                onClick={() => setIsOpen(!isOpen)}
              />
              {isOpen && (
                <ul style={{
                  position: "absolute",
                  top: "31px",
                  width: "490px",
                  maxHeight: "350px",
                  overflowY: "auto",
                  backgroundColor: "#fff",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  listStyle: "none",
                  padding: "0",
                  margin: "0",
                  zIndex: 1000,
                }}>
                  {filteredProfiles.length > 0 ? (
                    filteredProfiles.map((item, index) => (
                      <li
                        key={index}
                        onClick={() => handleProfileChange(item)}
                        style={{ cursor: "pointer", padding: "4px 10px" }}
                        className="dropdown-item"
                      >
                        {item.name}
                      </li>
                    ))
                  ) : (
                    <li
                      className="dropdown-item no-results"
                      style={{ padding: "8px", color: "#888" }}
                    >
                      No profiles found
                    </li>
                  )}
                </ul>
              )}
            </div>

            {/* Selected Client Display */}
            {/* {selectedclient && (
              <span style={{
                height: "30px",
                width: "200px",
                backgroundColor: "rgba(255, 255, 255, 0.8)",
                border: "1px solid #ccc",
                borderRadius: "5px",
                padding: "0 10px",
                display: 'flex',
                alignItems: 'center'
              }}>
                {selectedclient}
              </span>
            )} */}

            {/* Skills Input */}
            <input
              placeholder="Enter Skills..."
              style={{
                height: "30px",
                width: "200px",
                backgroundColor: "rgba(255, 255, 255, 0.80)",
                border: "1px solid #ccc",
                borderRadius: "5px",
                padding: "0 10px"
              }}
              value={selectedSkills}
              onChange={(e) => {
                const updatedSkills = e.target.value.split(',').map(skill => skill.trim());
                setSelectedSkills(updatedSkills);
                localStorage.setItem('selectedSkills', JSON.stringify(updatedSkills));
              }}
            />

            {/* Submit Button */}
            <button
              className="remove_filter_icons"
              onClick={handleSubmit}
              style={{
                display: 'flex',
                padding: '1px',
                border: "none",
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: "5px",
                height: "30px",
                backgroundColor: '#f1f1f1',
                cursor: !searchTerm.trim() ? 'not-allowed' : 'pointer'
              }}
              disabled={!searchTerm.trim()}
            >
              {waitForSubmission ? (
                <TailSpin height="25" width="25" color="#4fa94d" />
              ) : (
                <FcCheckmark
                  style={{
                    height: '25px', width: "25px", opacity: !searchTerm.trim() ? 0.5 : 1
                  }}
                  data-tooltip-id="Submit"
                  data-tooltip-content="Submit"
                />
              )}
              <ReactTooltip
                style={{ zIndex: 999, padding: "4px", backgroundColor: "#32406d" }}
                place="top-start"
                id="Submit"
              />
            </button>

            {/* Clear Search */}
            <div
              className="remove_filter_icons"
              onClick={() => {
                setSearchTerm('');
                setSelectedSkills([]);
                setSelectedclient('')
                setShowAllCandidates(true);
              }}
              style={{
                display: 'flex',
                padding: '3px',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: "5px",
                height: '30px',
                backgroundColor: '#f1f1f1',
                cursor: 'pointer'
              }}
            >
              <MdOutlineYoutubeSearchedFor
                style={{ height: '25px', width: "25px", color: "#32406d" }}
                data-tooltip-id="remove_search"
                data-tooltip-content="Clear search"
              />
              <ReactTooltip
                style={{ zIndex: 999, padding: "2px", backgroundColor: "#32406d" }}
                place="top-start"
                id="remove_search"
              />
            </div>
          </div>


        </div>


        <div
          className="dashr"
        // className={` mobilesh useraccco ${isAnimating ? 'genie-effect' : ''}`}
        >

        </div>

        <div
          className="dashcontainer"
          //  className={`dashcontainer ${isAnimating ? 'genie-effect' : ''}`}
          style={{
            position: "relative",
            width: "100%",
            padding: "5px 5px",
            background: "rgba(255, 255, 255, 0.25)",
            boxShadow: "0 8px 32px 0 rgba(31, 38, 135, 0.37)",
            backdropFilter: "blur(11.5px)",
            borderRadius: "10px",
            border: "1px solid rgba(255, 255, 255, 0.18)",
            height: "100%",
            overflow: "hidden",
          }}>

          <div className="table-container" style={{
            //height: "520px",
            overflow: "auto",
            marginTop: "5px"
          }}>


            <table className="max-width-fit-content table" style={{ width: "100%", tableLayout: "fixed", marginTop: "0" }} id="candidates-table">
              <thead>
                <tr>
                  <th style={{ width: "95px" }}> <span>Select all </span>

                    <input
                      style={{ cursor: "pointer" }}
                      id="all"
                      type="checkbox"
                      onChange={handleSelectAll}
                      checked={
                        filteredRows.length > 0 &&
                        (showAllCandidates
                          ? filteredRows.slice((id - 1) * 60, id * 60).every((item) =>
                            selectItems.some((selected) => selected.id === item.id)
                          )
                          : filteredRows.slice((currentPage - 1) * 60, currentPage * 60).every((item) =>
                            selectItems.some((selected) => selected.id === item.id)
                          )
                        )
                      }
                      data-tooltip-id={"select_all"}
                      data-tooltip-content="Select All"

                    />
                    <ReactTooltip
                      style={{ zIndex: 999, padding: "4px", backgroundColor: "#32406D", }}
                      place="bottom"
                      id="select_all"
                    />
                  </th>
                  <th style={{ color: showSearchjobassignment.showSearchdate ? "orange" : "white", fontSize: "13px" }} >
                    <span
                      id={"date_label_ref"}
                      onClick={() => {
                        // console.log("Filter icon clicked!");
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchdate"
                                ? !prev.showSearchdate
                                : false,
                            ]),
                          ),
                        }));
                      }}
                      style={{ cursor: "pointer" }}  >Applied Date</span>
                    <MdFilterAlt
                      style={{ color: isDateFiltered ? "orange" : "white" }}
                      id={"date_ref"}
                      className="arrow"
                      onClick={() => {
                        // console.log("Filter icon clicked!");
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchdate"
                                ? !prev.showSearchdate
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    />
                    {showSearchjobassignment.showSearchdate && (
                      <div
                        ref={uniRef}
                        className="Filter-popup"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <form
                          id="filter-form"
                          className="Filter-inputs-container"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllDate}
                                onChange={(e) => handleSelectAllForDate(e)}
                                onClick={(e) => e.stopPropagation()}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  fontSize: '13px',
                                  cursor: 'pointer',
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleSelectAllForDate(e);
                                }}>
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataDate
                                .sort((a, b) => {
                                  const inArray2a = dateSelected?.includes(a);
                                  const inArray2b = dateSelected?.includes(b);

                                  if (inArray2a && !inArray2b) {
                                    return -1;
                                  }
                                  else if (!inArray2a && inArray2b) {
                                    return 1;
                                  } else {
                                    return new Date(b) - new Date(a);
                                  }

                                })
                                .map((date_created, index) => (
                                  <div key={index} className="filter-inputs">
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={dateSelected.includes(
                                        date_created,
                                      )}
                                      onChange={(e) =>
                                        handleCheckboxChangeForDate(
                                          date_created,
                                          e
                                        )
                                      }
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleCheckboxChangeForDate(
                                          date_created,
                                          e
                                        );
                                      }}
                                    >
                                      {/* {date_created} */}
                                      {new Date(date_created).toISOString().split('T')[ 0 ]}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                        {/* <div className="filter-popup-footer">
                                                <button onClick={handleOkClick}>OK</button>
                                                <button
                                                  onClick={() => {
                                                    setshowSearchjobassignment((prev) =>
                                                      Object.fromEntries(
                                                        Object.entries(prev).map(
                                                          ([key, value]) => [key, false],
                                                        ),
                                                      ),
                                                    );
                                                  }}
                                                >
                                                  Cancel
                                                </button>
                                              </div> */}
                      </div>
                    )}
                  </th>

                  <th style={{ color: showSearchjobassignment.showSearchName ? "orange" : "white", fontSize: "13px" }}>
                    <span
                      style={{ cursor: "pointer" }}
                      id={"name_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchName"
                                ? !prev.showSearchName
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    >  Name  {" "}</span>

                    <MdFilterAlt
                      style={{ color: isnameFiltered ? "orange" : "white" }}
                      id={"name_ref"}
                      className="arrow"
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchName"
                                ? !prev.showSearchName
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    />
                    {showSearchjobassignment.showSearchName && (
                      <div
                        ref={uniRef}
                        className="Filter-popup"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <form
                          id="filter-form"
                          className="Filter-inputs-container"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAll}
                                onChange={(e) => handleSelectAllForName(e)}
                                onClick={(e) => e.stopPropagation()}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  fontSize: '13px',
                                  cursor: 'pointer',
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleSelectAllForName(e);
                                }}>
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataNames
                                .slice()
                                .filter((name) => name !== undefined)
                                .sort((a, b) => {
                                  // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                  const trimmedA = a?.trim().toLowerCase();
                                  const trimmedB = b?.trim().toLowerCase();
                                  const inArray2A = (nameSelected || []).includes(trimmedA);
                                  const inArray2B = (nameSelected || []).includes(trimmedB);

                                  if (inArray2A && !inArray2B) {
                                    return -1;
                                  } else if (!inArray2A && inArray2B) {
                                    return 1;
                                  } else {
                                    return trimmedA.localeCompare(trimmedB);
                                  }
                                })
                                .map((name, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={nameSelected.includes(
                                        name.toLowerCase(),
                                      )}
                                      onChange={(e) =>
                                        handleCheckboxChange(
                                          name.toLowerCase(),
                                          e
                                        )
                                      }
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleCheckboxChange(
                                          name.toLowerCase(),
                                          e
                                        );
                                      }}
                                    >
                                      {name}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>

                      </div>
                    )}
                  </th>
                  <th style={{ color: showSearchjobassignment.showSearchEmail ? "orange" : "white", fontSize: "13px" }}>
                    {/* <span
                      style={{ cursor: "pointer" }}
                      id={"email_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchEmail"
                                ? !prev.showSearchEmail
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    >
                    {" "}</span> */}
                    Email
                    {/* <MdFilterAlt
                      style={{
                        color: isemailFiltered ? "orange" : "white",
                      }}
                      id={"email_ref"}
                      className="arrow"
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchEmail"
                                ? !prev.showSearchEmail
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    /> */}
                    {/* {showSearchjobassignment.showSearchEmail && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form-email"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllEmail}
                                onChange={handleSelectAllForEmail}
                              />
                              <label
                                onClick={() => handleSelectAllForEmail()}
                                style={{
                                  fontSize: '13px',
                                  fontWeight: '400',
                                  cursor: 'pointer',
                                  marginBottom: "0px",

                                }}>
                                Select all
                              </label>

                            </li>
                            <li>
                              {uniqueDataEmail
                                .slice()
                                .sort((a, b) => {
                                  // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                  const trimmedA = a?.trim().toLowerCase();
                                  const trimmedB = b?.trim().toLowerCase();

                                  const inArray2A = emailSelected.includes(trimmedA);
                                  const inArray2B = emailSelected.includes(trimmedB);

                                  if (inArray2A && !inArray2B) {
                                    return -1;
                                  } else if (!inArray2A && inArray2B) {
                                    return 1;
                                  } else {
                                    return 0;
                                  }
                                })
                                .map((email, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={emailSelected.includes(
                                        email.toLowerCase(),
                                      )}
                                      onChange={() =>
                                        handleCheckboxChangeEmail(
                                          email.toLowerCase(),
                                        )
                                      }
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={() => handleCheckboxChangeEmail(
                                        email.toLowerCase(),
                                      )}
                                    >
                                      {email}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>

                      </div>
                    )} */}
                  </th>
                  <th style={{ color: showSearchjobassignment.showSearchMobile ? "orange" : "white", fontSize: "13px" }}>
                    {/* <span
                      style={{ cursor: "pointer" }}
                      id={"mobile_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchMobile"
                                ? !prev.showSearchMobile
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    > {" "}</span> */}
                    Mobile
                    {/* <MdFilterAlt
                      style={{
                        color: ismobileFiltered ? "orange" : "white",
                      }}
                      id={"mobile_ref"}
                      className="arrow"
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchMobile"
                                ? !prev.showSearchMobile
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    /> */}
                    {/* {showSearchjobassignment.showSearchMobile && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllMobile}
                                onChange={handleSelectAllForMobile}
                              />
                              <label
                                onClick={() => handleSelectAllForMobile()}
                                style={{
                                  cursor: 'pointer',
                                  marginBottom: '0px',
                                  fontWeight: '400',
                                  fontSize: '13px',
                                }}>
                                Select all
                              </label>

                            </li>
                            <li>
                              {uniqueDataMobile
                                .slice()
                                .sort((a, b) => {
                                  // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                  const trimmedA = a?.trim();
                                  const trimmedB = b?.trim();

                                  const inArray2A = mobileSelected.includes(trimmedA);
                                  const inArray2B = mobileSelected.includes(trimmedB);

                                  if (inArray2A && !inArray2B) {
                                    return -1;
                                  } else if (!inArray2A && inArray2B) {
                                    return 1;
                                  } else {
                                    return 0;
                                  }
                                })
                                .map((mobile, index) => (
                                  <div key={index} className="filter-inputs">
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={mobileSelected.includes(
                                        mobile,
                                      )}
                                      onChange={() =>
                                        handleCheckBoxChangeForMobile(mobile)
                                      }
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={() => handleCheckBoxChangeForMobile(mobile)}
                                    >
                                      {mobile}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>

                      </div>
                    )} */}
                  </th>
                  <th style={{ color: showSearchjobassignment.showSearchClient ? "orange" : "white", fontSize: "13px" }}>
                    <span
                      style={{ cursor: "pointer" }}
                      id={"client_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchClient"
                                ? !prev.showSearchClient
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    >Client{" "}</span>
                    <MdFilterAlt
                      style={{
                        color: isclientFiltered ? "orange" : "white",
                      }}
                      id={"client_ref"}
                      className="arrow"
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchClient"
                                ? !prev.showSearchClient
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    />
                    {showSearchjobassignment.showSearchClient && (
                      <div ref={uniRef} className="Filter-popup">
                        <form
                          id="filter-form-client"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllClient}
                                onChange={handleSelectAllForClient}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => handleSelectAllForClient()}
                              >
                                Select all
                              </label>

                            </li>
                            <li>
                              {uniqueDataClient
                                .slice()
                                .sort((a, b) => {
                                  // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                  const trimmedA = a?.trim().toLowerCase();
                                  const trimmedB = b?.trim().toLowerCase();

                                  const inArray2A = clientSelected?.includes(trimmedA);
                                  const inArray2B = clientSelected?.includes(trimmedB);

                                  if (inArray2A && !inArray2B) {
                                    return -1;
                                  } else if (!inArray2A && inArray2B) {
                                    return 1;
                                  } else {
                                    return trimmedA.localeCompare(trimmedB);
                                  }
                                })
                                .map((client, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={clientSelected.includes(
                                        client.toLowerCase(),
                                      )}
                                      onChange={(e) => {
                                        handleCheckboxChangeClient(
                                          client.toLowerCase(),
                                          e
                                        )
                                      }}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation()
                                          ; handleCheckboxChangeClient(
                                            client.toLowerCase(),
                                            e
                                          )
                                      }}
                                    >
                                      {client}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                        {/* <div className="filter-popup-footer">
                                             <button onClick={handleOkClick}>OK</button>
                                             <button
                                               onClick={() => {
                                                 setshowSearchjobassignment((prev) =>
                                                   Object.fromEntries(
                                                     Object.entries(prev).map(
                                                       ([key, value]) => [key, false],
                                                     ),
                                                   ),
                                                 );
                                               }}
                                             >
                                               Cancel
                                             </button>
                                           </div> */}
                      </div>
                    )}
                  </th>
                  {/* <th style={{ width: "75px", color: showSearchjobassignment.showSearchProfile ? "orange" : "white", fontSize: "13px" }}>
                                      <span
                                        style={{ cursor: "pointer" }}
                                        id={"profile_label_ref"}
                                        onClick={() => {
                                          setshowSearchjobassignment((prev) => ({
                                            ...Object.fromEntries(
                                              Object.keys(prev).map((key) => [
                                                key,
                                                key === "showSearchProfile"
                                                  ? !prev.showSearchProfile
                                                  : false,
                                              ]),
                                            ),
                                          }));
                                        }}
                                      >Profile{" "}</span>
                                      <MdFilterAlt
                                        style={{
                                          color: isprofileFiltered ? "orange" : "white",
                                        }}
                                        id={"profile_ref"}
                                        name=""
                                        className="arrow"
                                        onClick={() => {
                                          setshowSearchjobassignment((prev) => ({
                                            ...Object.fromEntries(
                                              Object.keys(prev).map((key) => [
                                                key,
                                                key === "showSearchProfile"
                                                  ? !prev.showSearchProfile
                                                  : false,
                                              ]),
                                            ),
                                          }));
                                        }}
                                      />
                                      {showSearchjobassignment.showSearchProfile && (
                                        <div ref={uniRef} className="Filter-popup">
                                          <form
                                            id="filter-form-profile"
                                            className="Filter-inputs-container"
                                          >
                                            <ul>
                                              <li>
                                                <input
                                                  type="checkbox"
                                                  style={{
                                                    width: "12px",
                                                    marginRight: "5px",
                                                  }}
                                                  checked={selectAllProfile}
                                                  onChange={handleSelectAllForProfile}
                                                />
                                                <label
                                                  style={{
                                                    marginBottom: "0px",
                                                    fontWeight: "400",
                                                    cursor: 'pointer',
                                                    fontSize: '13px',
                                                  }}
                                                  onClick={() => handleSelectAllForProfile()} >
                                                  Select all
                                                </label>
                                              </li>
                                              <li>
                                                {uniqueDataProfile
                                                  .slice()
                                                  .sort((a, b) => {
                                                    // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                                    const trimmedA = a?.trim().toLowerCase();
                                                    const trimmedB = b?.trim().toLowerCase();

                                                    const inArray2A = profileSelected.includes(trimmedA);
                                                    const inArray2B = profileSelected.includes(trimmedB);

                                                    if (inArray2A && !inArray2B) {
                                                      return -1;
                                                    } else if (!inArray2A && inArray2B) {
                                                      return 1;
                                                    } else {
                                                      return 0;
                                                    }
                                                  })
                                                  .map((profile, index) => (
                                                    <div
                                                      key={index}
                                                      className="filter-inputs"
                                                    >
                                                      <input
                                                        type="checkbox"
                                                        style={{
                                                          width: "12px",
                                                        }}
                                                        checked={profileSelected.includes(
                                                          profile?.toLowerCase(),
                                                        )}
                                                        onChange={(e) =>{
                                                          handleCheckboxChangeProfile(
                                                            profile.toLowerCase(),
                                                            e
                                                          )
          }          }
                                                      />
                                                      <label
                                                        style={{
                                                          marginBottom: "0px",
                                                          fontWeight: "400",
                                                          cursor: 'pointer',
                                                        }}
                                                        onClick={() => {
                                                             e.stopPropagation();handleCheckboxChangeProfile(
                                                          profile.toLowerCase(),
                                                          e
)}}
                                                      >
                                                        {profile}
                                                      </label>
                                                    </div>
                                                  ))}
                                              </li>
                                            </ul>
                                          </form>

                                        </div>
                                      )}
                                    </th> */}

                  <th style={{ color: showSearchjobassignment.showSearchSkill ? "orange" : "white", fontSize: "13px" }}>
                    <span
                      style={{ cursor: "pointer" }}
                      id={"skills_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchSkill"
                                ? !prev.showSearchSkill
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    >Skills{" "}</span>
                    <MdFilterAlt
                      style={{
                        color: isskillFiltered ? "orange" : "white",
                      }}
                      id={"skills_ref"}
                      className="arrow"
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchSkill"
                                ? !prev.showSearchSkill
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    />
                    {showSearchjobassignment.showSearchSkill && (
                      <div
                        ref={uniRef}
                        className="Filter-popup"
                        style={{ width: "300px", marginLeft: "20px" }}
                      >
                        <form
                          id="filter-form"
                          className="Filter-inputs-container"
                        >
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllSkill}
                                onChange={handleSelectAllForSkill}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => handleSelectAllForSkill()} >
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataSkill
                                .filter((skill) => skill !== null && skill !== "" && skill !== undefined)
                                .slice()
                                .sort((a, b) => {
                                  // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                  const trimmedA = a?.trim().toLowerCase();
                                  const trimmedB = b?.trim().toLowerCase();

                                  const inArray2A = skillSelected?.includes(trimmedA);
                                  const inArray2B = skillSelected?.includes(trimmedB);

                                  if (inArray2A && !inArray2B) {
                                    return -1;
                                  } else if (!inArray2A && inArray2B) {
                                    return 1;
                                  } else {
                                    return 0;
                                  }
                                })
                                .map((skills, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={skillSelected.includes(
                                        skills.toLowerCase(),
                                      )}
                                      onChange={(e) => {
                                        handleCheckboxChangeSkill(
                                          skills.toLowerCase(),
                                          e
                                        )
                                      }}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleCheckboxChangeSkill(
                                          skills.toLowerCase(),
                                          e
                                        )
                                      }}
                                    >
                                      {skills}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                        {/* <div className="filter-popup-footer">
                                               <button onClick={handleOkClick}>OK</button>
                                               <button
                                                 onClick={() => {
                                                   setshowSearchjobassignment((prev) =>
                                                     Object.fromEntries(
                                                       Object.entries(prev).map(
                                                         ([key, value]) => [key, false],
                                                       ),
                                                     ),
                                                   );
                                                 }}
                                               >
                                                 Cancel
                                               </button>
                                             </div> */}
                      </div>
                    )}
                  </th>

                  <th style={{ color: showSearchjobassignment.showSearchStatus ? "orange" : "white", fontSize: "13px" }}>
                    <span
                      style={{ cursor: "pointer" }}
                      id={"status_label_ref"}
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchStatus"
                                ? !prev.showSearchStatus
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    >Status{" "}</span>
                    <MdFilterAlt
                      style={{
                        color: isstatusFiltered || isColorFiltered ? "orange" : "white",
                      }}
                      id={"status_ref"}
                      className="arrow"
                      onClick={() => {
                        setshowSearchjobassignment((prev) => ({
                          ...Object.fromEntries(
                            Object.keys(prev).map((key) => [
                              key,
                              key === "showSearchStatus"
                                ? !prev.showSearchStatus
                                : false,
                            ]),
                          ),
                        }));
                      }}
                    />
                    {showSearchjobassignment.showSearchStatus && (
                      <div ref={uniRef} className="Filter-popup" style={{ marginLeft: "20px" }}>
                        <form
                          id="filter-form-client"
                          className="Filter-inputs-container"
                        >
                          {/* <div style={{ backgroundColor: "#cad1ff", height: "65px" }}>
                                          <p>Filter by color</p>
                                          <div style={{ display: "flex", justifyContent: "space-around", marginBottom: "10px" }}>

                                            <input
                                              type="radio"
                                              id="green"
                                              name="green"
                                              value="green"
                                              checked={selectedColors[0]}
                                              onClick={() => handleSelectedColors(0)}

                                              style={{
                                                appearance: 'none',
                                                width: '20px',
                                                height: '20px',
                                                borderRadius: '50%',
                                                border: '2px solid green',
                                                outline: 'none',
                                                marginRight: '5px',
                                                backgroundColor: selectedColors[0] ? 'green' : 'white',
                                              }}
                                            />
                                            <input
                                              type="radio"
                                              id="red"
                                              name="red"
                                              value="red"
                                              checked={selectedColors[1]}
                                              onClick={() => handleSelectedColors(1)}
                                              style={{
                                                appearance: 'none',
                                                width: '20px',
                                                height: '20px',
                                                borderRadius: '50%',
                                                border: '2px solid orange', // Red border for default and selected state
                                                outline: 'none',
                                                marginRight: '5px',
                                                backgroundColor: selectedColors[1] ? 'orange' : 'white',
                                              }}
                                            />
                                            <input
                                              type="radio"
                                              id="red"
                                              name="red"
                                              value="red"
                                              checked={selectedColors[2]}
                                              onClick={() => handleSelectedColors(2)}
                                              style={{
                                                appearance: 'none',
                                                width: '20px',
                                                height: '20px',
                                                borderRadius: '50%',
                                                border: '2px solid red',
                                                outline: 'none',
                                                marginRight: '5px',
                                                backgroundColor: selectedColors[2] ? 'red' : 'white',
                                              }}
                                            />

                                          </div>
                                        </div> */}
                          {/* color for status */}
                          <ul>
                            <li>
                              <input
                                type="checkbox"
                                style={{
                                  width: "12px",
                                  marginRight: "5px",
                                }}
                                checked={selectAllStatus}
                                onChange={handleSelectAllForStatus}
                              />
                              <label
                                style={{
                                  marginBottom: "0px",
                                  fontWeight: "400",
                                  cursor: 'pointer',
                                  fontSize: '13px',
                                }}
                                onClick={() => handleSelectAllForStatus()} >
                                Select all
                              </label>
                            </li>
                            <li>
                              {uniqueDataStatus
                                .filter(
                                  (recruiter) =>
                                    recruiter != null && recruiter !== "",
                                )
                                .slice()
                                .sort((a, b) => {
                                  // const array2 = ["kiwi", "papaya", "orange"]; // Replace this with your actual array
                                  const trimmedA = a?.trim().toLowerCase();
                                  const trimmedB = b?.trim().toLowerCase();

                                  const inArray2A = statusSelected.includes(trimmedA);
                                  const inArray2B = statusSelected.includes(trimmedB);

                                  if (inArray2A && !inArray2B) {
                                    return -1;
                                  } else if (!inArray2A && inArray2B) {
                                    return 1;
                                  } else {
                                    return trimmedA.localeCompare(trimmedB);
                                  }
                                })
                                .map((status, index) => (
                                  <div
                                    key={index}
                                    className="filter-inputs"
                                  >
                                    <input
                                      type="checkbox"
                                      style={{
                                        width: "12px",
                                      }}
                                      checked={statusSelected.includes(
                                        status.toLowerCase(),
                                      )}
                                      onChange={(e) => {
                                        handleCheckboxChangeStatus(
                                          status.toLowerCase(),
                                          e
                                        )
                                      }}
                                    />
                                    <label
                                      style={{
                                        marginBottom: "0px",
                                        fontWeight: "400",
                                        cursor: 'pointer',
                                      }}
                                      onClick={(e) => {

                                        e.stopPropagation();
                                        handleCheckboxChangeStatus(
                                          status.toLowerCase(),
                                          e
                                        )
                                      }}
                                    >
                                      {status}
                                    </label>
                                  </div>
                                ))}
                            </li>
                          </ul>
                        </form>
                        {/* <div className="filter-popup-footer">
                                            <button onClick={handleOkClick}>OK</button>
                                            <button
                                              onClick={() => {
                                                setshowSearchjobassignment((prev) =>
                                                  Object.fromEntries(
                                                    Object.entries(prev).map(
                                                      ([key, value]) => [key, false],
                                                    ),
                                                  ),
                                                );
                                              }}
                                            >
                                              Cancel
                                            </button>
                                          </div> */}
                      </div>
                    )}
                  </th>
                  <th style={{ width: "80px" }}>Resume</th>
                </tr>
              </thead>
              {!hasSubmitted ? (
                <tbody>
                  <tr>
                    <td colSpan="9" style={{ textAlign: "center", fontSize: "16px", padding: "10px", color: "grey" }}>
                      Please submit profile  to show Data
                    </td>
                  </tr>
                </tbody>
              ) : loading ? (
                <tbody>
                  <tr>
                    <td colSpan="9" style={{ textAlign: "center", fontSize: "16px", padding: "10px", color: "grey" }}>

                      <Hourglass
                        height="100"
                        width="60"
                        ariaLabel="hourglass-loading"
                        wrapperStyle={{}}
                        wrapperClass=""
                        colors={[ "#306cce", "#72a1ed" ]}
                        style={{ zIndex: "999" }}
                      />
                      <p className="loader-text" color="green">Loading...</p>

                    </td>
                  </tr>
                </tbody>
              ) : (
                <tbody className="scrollable-body resume">
                  {filteredRows?.length === 0 ? (
                    <tr>
                      <td colSpan="9" style={{ textAlign: "center", fontSize: "16px", padding: "10px", color: "red" }}>
                        No candidates found for this role.
                      </td>
                    </tr>
                  ) : (
                    filteredRows.slice((currentPage - 1) * 60, currentPage * 60).map((candidate, idx) => (
                      <tr key={candidate.candidate_id || idx}>

                        <td>
                          <input
                            type="checkbox"
                            style={{ cursor: "pointer" }}
                            checked={selectItems.some(selectItem => selectItem.id === candidate.id)}
                            onChange={(e) => handleSelectIndividual(candidate.id, e)}
                          />

                        </td>
                        <td>{new Date(candidate.date_created).toISOString().split('T')[ 0 ]}</td>

                        <td style={{
                          textAlign: "left",
                          padding: "5px",
                          borderBottom: "1px solid #ddd",
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                        }}>{candidate.name}</td>

                        <td
                          name="email_td"
                          id={showItems[ idx ]?.id + "2"}
                          style={{
                            padding: "5px",
                            borderBottom: "1px solid #ddd",
                            textAlign: "left",
                            // whiteSpace: "normal",
                            // wordWrap: "break-word",
                          }}>

                          {candidate.email}
                          {showItems && showItems[ idx ] && showItems[ idx ].email ? (
                            <div
                              id={"default2"}
                              style={{
                                position: "absolute",
                                Width: "auto",
                                maxWidth: "350px",
                                height: "auto",
                                backgroundColor: "rgba(255, 255, 255, 0.8)",
                                border: "1px solid #666",
                                borderRadius: "10px",
                                padding: "10px",
                                boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                                zIndex: "9999", // Ensure the div appears above other content
                                wordWrap: "break-word",
                                whiteSpace: "normal",
                              }}
                            >
                              {candidate.email}
                            </div>
                          ) : (
                            ""
                          )}
                        </td>
                        <td>
                          {candidate.mobile}
                        </td>
                        <td
                          style={{
                            textAlign: "left"
                          }}
                        >{candidate.client}</td>
                        {/* <td
                    onClick={() => toggleRowSelection(item)}
                    id={list[idx]?.id + "2"}
                    style={{
                      textAlign: "left",
                      padding: "5px",
                      borderBottom: "1px solid #ddd",
                    }}
                  >
                    {item.profile}
                    {list && list[idx]?.profile && (
                      <div
                        style={{
                          position: "absolute",
                          maxWidth: "350px",
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          border: "1px solid #666",
                          borderRadius: "10px",
                          padding: "10px",
                          boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                          zIndex: 9999,
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                        }}
                      >
                        {item.profile}
                      </div>
                    )}
                  </td> */}
                        {/* <td style={{
                          padding: "5px",
                          borderBottom: "1px solid #ddd",
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                        }}>{candidate.phone}</td> */}
                        <td id={showItems[ idx ]?.id + "1"}
                          style={{
                            // position:'relative',
                            textAlign: "left",
                            padding: "5px",
                            borderBottom: "1px solid #ddd",
                          }}>
                          {candidate.skills || "No skills available"}
                          {showItems && showItems[ idx ] && showItems[ idx ].skills ? (
                            <div
                              id={"default1"}
                              style={{
                                position: "absolute",
                                Width: "auto",
                                maxWidth: "350px",
                                height: "auto",
                                backgroundColor: "rgba(255, 255, 255, 0.8)",
                                border: "1px solid #666",
                                borderRadius: "10px",
                                padding: "10px",
                                boxShadow: "0px 0px 10px rgba(0,0,0,0.2)",
                                zIndex: "9999", // Ensure the div appears above other content
                                wordWrap: "break-word",
                                whiteSpace: "normal",
                              }}
                            >
                              {candidate.skills || "No skills available"}
                            </div>
                          ) : null}


                        </td>
                        <td style={{
                          textAlign: "left",
                          padding: "5px",
                          borderBottom: "1px solid #ddd",
                          whiteSpace: "normal",
                          wordWrap: "break-word",
                          color: getStatusColor(candidate.status),
                        }}
                        >{candidate.status}</td>
                        <td style={{ width: "60px" }}>
                          <FontAwesomeIcon
                            // data-tooltip-id={
                            //     candidate["resume_present"] !== true
                            //     ? "my-tooltip"
                            //     : "random-tooltip"
                            // }
                            // data-tooltip-content="Resume not available"
                            icon={faFileAlt}
                            // className={
                            //     candidate["resume_present"] === true
                            //     ? "resume_option"
                            //     : "avoid_resume_option"
                            // }
                            style={{
                              color: candidate.resume_base64 ? "green" : "gray",
                              fontSize: "18px",
                              cursor: "pointer"
                            }}
                            onClick={() => {

                              resumeApiCall(candidate);

                            }}
                          />
                          {/* {console.log(item.resume,"dashboardresume")} */}
                          {/* <ReactTooltip
                            style={{ zIndex: 999, padding: "4px" }}
                            place="bottom"
                            variant="error"
                            id="my-tooltip"
                          /> */}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              )}
            </table>


          </div>
        </div>


        {showAllCandidates ? (
          <div
            style={{

            }}
            className="dashbottom"
          >
            <div>
              Showing {belowCount === 0 ? 0 : (id - 1) * 60 + 1} to{" "}
              {id * 60 <= belowCount ? id * 60 : belowCount} of {belowCount}{" "}
              entries
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginTop: "10px",
              }}
              className="pagination"
            >
              <ul className="page">
                <li
                  className="page__btn newpage_btn"
                  style={{
                    padding: "1px 5px",
                    marginRight: "5px",
                    cursor: "pointer",
                    alignItems: "center",
                    color: "#32406d",
                  }}
                  onClick={() => {
                    if (id !== 1) {
                      setId(id - 1);  // Go to previous page if not the first page
                    } else {
                      toast.warn("You have reached the starting page already.", {
                        position: "top-right",
                        autoClose: 3000,
                        hideProgressBar: false,
                        closeOnClick: true,
                        pauseOnHover: true,
                        draggable: true,
                        progress: undefined,
                        theme: "dark",
                        transition: Bounce,
                      });  // Show warning toast if already on first page
                    }
                  }}
                >
                  <FaAngleLeft style={{ marginTop: "3px" }} />
                </li>
                <div className="gap" style={{ display: "flex", columnGap: "10px" }}>

                  {getPageRange().map((pageNumber, index) => (
                    <button
                      className={
                        pageNumber === id ? "pag_buttons" : "unsel_button"
                      }
                      key={index}
                      onClick={() => goToPage(pageNumber)}
                      style={{
                        fontWeight: pageNumber === id ? "bold" : "normal",
                        marginRight: "10px",
                        color: pageNumber === id ? "white" : "#000000", // Changed text color
                        backgroundColor:
                          pageNumber === id ? "#32406d" : "#ffff", // Changed background color
                        borderRadius: pageNumber === id ? "0.2rem" : "",
                        fontSize: "15px",
                        border: "none",
                        padding: "1px 10px", // Adjusted padding
                        cursor: "pointer", // Added cursor pointer
                      }}
                      class="page__numbers"
                    >
                      {pageNumber}
                    </button>
                  ))}

                </div>
                <li
                  className="page__btn newpage_btn"
                  style={{
                    padding: "1px 5px",
                    cursor: "pointer",
                    color: "#32406d",
                    marginLeft: "3px"
                  }}
                  onClick={() => {
                    if (belowCount > id * 60) setId(id + 1);
                    else {
                      toast.warn("Reached the end of the list", {
                        position: "top-right",
                        autoClose: 3000,
                        hideProgressBar: false,
                        closeOnClick: true,
                        pauseOnHover: true,
                        draggable: true,
                        progress: undefined,
                        theme: "dark",
                        transition: Bounce,
                      });
                      setId(id);
                    }
                  }}
                >
                  <FaAngleRight style={{ marginTop: "3px" }} />
                </li>
              </ul>
            </div>
          </div>

        ) : (
          <div
            style={{

            }}
            className="dashbottom"
          >
            <div>
              Showing {lowCount === 0 ? 0 : (currentPage - 1) * 60 + 1} to{" "}
              {currentPage * 60 <= lowCount ? currentPage * 60 : lowCount} of {lowCount}{" "}
              entries
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                marginTop: "10px",
              }}
              className="pagination"
            >
              <ul className="page">
                <li
                  className="page__btn newpage_btn"
                  style={{
                    padding: "1px 5px",
                    marginRight: "5px",
                    cursor: "pointer",
                    alignItems: "center",
                    color: "#32406d",
                  }}
                  onClick={() => {
                    currentPage !== 1 ? setCurrentPage(currentPage - 1) : setCurrentPage(currentPage);
                  }}
                >
                  <FaAngleLeft style={{ marginTop: "3px" }} />
                </li>
                <div className="gap" style={{ display: "flex", columnGap: "10px" }}>

                  {getPageRangesearch().map((pageNumber, index) => (

                    <button
                      className={
                        pageNumber === currentPage ? "pag_buttons" : "unsel_button"
                      }
                      key={index}
                      onClick={() => goToPagesearch(pageNumber)}

                      style={{
                        fontWeight: pageNumber === currentPage ? "bold" : "normal",
                        marginRight: "10px",
                        color: pageNumber === currentPage ? "white" : "#000000", // Changed text color
                        backgroundColor:
                          pageNumber === currentPage ? "#32406d" : "#ffff", // Changed background color
                        borderRadius: pageNumber === currentPage ? "0.2rem" : "",
                        fontSize: "15px",
                        border: "none",
                        padding: "1px 10px", // Adjusted padding
                        cursor: "pointer", // Added cursor pointer
                      }}
                      class="page__numbers"
                    >
                      {pageNumber}
                    </button>
                  ))}

                </div>
                <li
                  className="page__btn newpage_btn"
                  style={{
                    padding: "1px 5px",
                    cursor: "pointer",
                    color: "#32406d",
                    marginLeft: "3px"
                  }}
                  onClick={() => {
                    if (lowCount > currentPage * 60) setCurrentPage(currentPage + 1);
                    else {
                      toast.warn("Reached the end of the list", {
                        position: "top-right",
                        autoClose: 3000,
                        hideProgressBar: false,
                        closeOnClick: true,
                        pauseOnHover: true,
                        draggable: true,
                        progress: undefined,
                        theme: "dark",
                        transition: Bounce,
                      });
                      setCurrentPage(currentPage);
                    }
                  }}
                >
                  <FaAngleRight style={{ marginTop: "3px" }} />
                </li>
              </ul>
            </div>
          </div>
        )}

      </div>


      <ReactTooltip />


      <Modal
        isOpen={showEmailModal}
        onRequestClose={() => closeModal()}
        contentLabel="Recipient Email Selection"
        className="modal-content"
        overlayClassName="modal-overlay"
        style={{
          overlay: {

            zIndex: 9999,
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            width: "70%",
            // height: "auto",
            height: "95%", // Limit max height
            display: "flex",
            flexDirection: "column",
            background: "#ffffff",
            borderRadius: "10px",
            boxShadow: "0px 6px 20px rgba(0, 0, 0, 0.3)", // Softer shadow
            padding: "20px",
          },
        }}
      >
        <h2 style={{ margin: 0, marginBottom: '15px', textAlign: "left", fontWeight: '600', color: '#333' }}>Email Format</h2>
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="emailSubject" style={{ display: 'block', fontWeight: '500', marginBottom: '5px' }}>Subject</label>
          <input
            type="text"
            id="emailSubject"
            value={emailSubject}
            onChange={(e) => setEmailSubject(e.target.value)}
            placeholder="Enter subject"
            style={{
              width: '100%',
              padding: '10px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              fontSize: '16px',
              outline: 'none',
            }}
          />
        </div>
        {/* <div >
          <label htmlFor="emailSubject" style={{ display: 'block', fontWeight: '500', marginBottom: '5px' }}>Body </label>
           <input
                  type="text"
                  id="emailSubject"
                  value={emailBody}
                  onChange={(e) => setEmailBody(e.target.value)}
                  placeholder="Enter subject"
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '16px',
                    outline: 'none',
                    overflow: "auto"
                  }}
                />
          <textarea
            rows={40}
            value={emailPreview}

            style={{ width: '100%',height:"360px", padding: '10px', fontFamily: 'monospace',border:"none" }}
          />
        </div> */}
        <div style={{ marginBottom: '15px' }}>
          <label htmlFor="emailBody" style={{ display: 'block', fontWeight: '500', marginBottom: '5px' }}>Body</label>
          <div
            id="emailBody"
            ref={bodyRef}
            contentEditable
            suppressContentEditableWarning
            style={{
              width: '100%',
              height: '360px',
              padding: '10px',
              border: 'none',
              borderRadius: '4px',
              fontSize: '16px',
              whiteSpace: 'pre-wrap',
              overflowY: 'auto',
            }}
          >
            Dear  <strong><span contentEditable="false" >{'{candidate_name}'}</span></strong>,<br />
            I hope this email finds you well. My name is <span contentEditable="false">{'{recruiter_name}'}</span>, and I am reaching out to you from Makonis Software Solutions. We recently reviewed your profile for an exciting job opportunity, and I wanted to see if you're open to exploring new roles.<br />
            We believe you could be a great fit for the position of <strong><span contentEditable="false">{'{job_role}'}</span></strong>. Below are some key details:<br />
            <table style={{
              margin: '20px auto',
              borderCollapse: 'collapse',
              width: '80%',
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
              fontFamily: 'Arial, sans-serif'
            }}>
              <thead>
                <tr style={{ backgroundColor: '#f0f0f0', color: '#333', textAlign: 'left' }}>
                  <th style={{ padding: '12px', borderBottom: '1px solid #ccc', fontSize: "19px", fontWeight: "600" }}>Client</th>
                  <th style={{ padding: '12px', borderBottom: '1px solid #ccc', fontSize: "19px", fontWeight: "600" }}>Location</th>
                  <th style={{ padding: '12px', borderBottom: '1px solid #ccc', fontSize: "19px", fontWeight: "600" }}>Mode</th>
                  <th style={{ padding: '12px', borderBottom: '1px solid #ccc', fontSize: "19px", fontWeight: "900" }}>Budget</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td style={{ padding: '10px', borderBottom: '1px solid #eee' }}>
                    <span contentEditable="false">{'{client}'}</span>
                  </td>
                  <td style={{ padding: '10px', borderBottom: '1px solid #eee' }}>
                    <span contentEditable="false">{'{location}'}</span>
                  </td>
                  <td style={{ padding: '10px', borderBottom: '1px solid #eee' }}>
                    <span contentEditable="false">{'{mode}'}</span>
                  </td>
                  <td style={{ padding: '10px', borderBottom: '1px solid #eee' }}> {'{budget}'} </td>
                </tr>
              </tbody>
            </table>

            {/* - <strong>Client</strong>: <br />
          - <strong>Location</strong>:<br />
          - <strong>Mode</strong>: <br />
          - <strong>Notice Period</strong>: <br />
          - <strong>Budget</strong>: <br /><br /> */}
            If you're interested, I'd love to set up a time to discuss this further. Please let me know your availability or feel free to reply with any questions.<br /><br />
            Looking forward to hearing from you!<br /><br />
            Best regards,<br />
            {/* <span contentEditable="false">{'{recruiter_name}'}</span><br />
            <span>
              {user_type === "management"
                ? "Sr Manager"
                : user_type === "recruiter"
                  ? "Sr Recruiter"
                  : "User"}
            </span><br /> */}
            <h4>Head - Talent Acquisition </h4>
            Makonis Software Solutions<br />

          </div>
        </div>


        <div style={{ display: 'flex', justifyContent: "right" }}>
          <button
            onClick={() => {
              setShowEmailModal(false)
              // setEmailSubject("")

            }}
            style={{
              backgroundColor: '#dc3545', // Red for cancel
              color: 'white',
              border: 'none',
              padding: '5px 15px',
              borderRadius: '4px',
              marginRight: "10px",
              cursor: 'pointer',
              transition: 'background-color 0.3s',
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleEmailSend}
            style={{
              backgroundColor: '#28a745', // Green for success
              color: 'white',
              border: 'none',
              padding: '5px 15px',
              borderRadius: '4px',
              marginRight: '10px',
              width: "77px",
              cursor: 'pointer',
              transition: 'background-color 0.3s',
            }}
          >
            {waitForSubmissionemail ? "" : "Send"}
            <ThreeDots
              wrapperClass="ovalSpinner"
              wrapperStyle={{
                position: "relative",
                left: "5px"
              }}
              visible={waitForSubmissionemail}
              height="25"
              width="35"
              color="white"
              ariaLabel="oval-loading"
            />
          </button>

        </div>
      </Modal>

      <Modal
        isOpen={showImageModal} // Control modal visibility
        onRequestClose={closeModals} // Close modal when clicking outside
        contentLabel="Profile Image"
        style={{
          overlay: {
            // backgroundColor: 'rgba(0, 0, 0, 0.7)', // Dark overlay
            background: "transparent",
            display: 'flex',
            justifyContent: 'center',
            bottom: "65%",
            left: "58%",
            alignItems: 'center', // Ensures overlay content is centered
            zIndex: 1000, // Make sure modal appears above other content
          },
          content: {
            position: "relative",
            backgroundColor: '#0288d1',
            borderRadius: '10px',
            padding: '10px',
            maxWidth: '35vw',
            maxHeight: '70vh',
            margin: 'auto',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            border: 'none',
            outline: 'none', // Removes the default outline
            zIndex: 1001, // Ensures modal content appears above overlay
          },
        }}
      >
        <div style={{ position: 'relative', display: "flex", justifyContent: "space-between", flexDirection: "row", alignItems: "center" }}>




          {/* {warningMessage} */}
          <p style={{ color: "#fff", marginBottom: '10px', fontSize: "18px" }}>{warningMessage} </p>
          <span
            onClick={closeModals}
            style={{
              position: 'relative',
              top: '-5px',
              right: '-6px',
              // backgroundColor: 'rgba(255, 255, 255, 0.8)',
              border: 'none',
              borderRadius: '50%',
              padding: '1px',
              fontSize: "25px",
              color: "#fff",
              cursor: 'pointer',
            }}
          >
            <MdCancel />
          </span>
        </div>
      </Modal>
    </div>

  );
}

export default ResumeFetch;

