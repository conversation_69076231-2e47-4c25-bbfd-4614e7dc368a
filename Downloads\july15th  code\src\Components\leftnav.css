

* {
  list-style: none;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}

body {
  background: #cad1ff;
  background-size: cover;
  background-origin: border-box;
  background-position: center;
  background-attachment: fixed;
  font-family: "Poppins", sans-serif;
}

.wrapper .sidebar {
  background: #ffffff;
  /* background: #161621; */
  position: fixed;
  top: 0;
  left: 0;
  width: 220px;
  height: 100vh;
  padding: 20px 0;
  z-index: 99;
  transition: all 0.5s ease;
}

/* profile pic */
.wrapper .sidebar .profile {
  margin-bottom: 30px;
  text-align: center;
}

.wrapper .sidebar .profile img {
  display: block;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin: 0 auto;
}

.wrapper .sidebar .profile h3 {
  color: #000000;
  margin: 10px 0 5px;
  font-size: 19px;
  font-weight: 500;
}

.wrapper .sidebar .profile p {
  color: rgb(15, 15, 15);
  font-size: 14px;
}

/* Menu Items */
.wrapper .sidebar ul li a {
  display: block;
  padding: 4.5px 27px;
  border-bottom: 1px solid rgb(59, 59, 61);
  color: rgb(0, 0, 0);
  font-size: 14px;
  position: relative;
  text-decoration: none;
}
.nav-link.active {
  background-color: #32406d;
  color: #ffffff !important; /* Color when active or hovered */
  padding-right: 0px;
}
/* .wrapper .sidebar ul {
    padding-left: 0;
} */

.wrapper .sidebar ul li a .icon {
  color: #dee4ec;
  width: 30px;
  display: inline-block;
}

.wrapper .sidebar ul li a .item {
  color: #ffffff;
}

.wrapper .sidebar ul li a:hover {
  color: #ffffff;
  background: #c9d0fe;
  padding-right: 0px;
  /* border-right: 5px solid rgba(69, 120, 216,  0.493); */
}

.wrapper .sidebar ul li a:hover .icon,
.wrapper .sidebar ul li a.active .icon {
  color: #0c7db1;
}

/* .wrapper .sidebar ul li a:hover:before,
.wrapper .sidebar ul li a.active:before {
    display: block;
} */

.heading2 {
  padding-top: 55px;
  text-align: center;
}

#lgout {
  display: block;
 width: 100%;
  padding: 4.5px 27px;
  border-bottom: 1px solid rgb(59, 59, 61);
  color: rgb(0, 0, 0);
  font-size: 14px;
  cursor: pointer;
}
#resume {
  display: block;
  padding: 4.5px 27px;
  border-bottom: 1px solid rgb(59, 59, 61);
  color: rgb(0, 0, 0);
  font-size: 14px;
  cursor: pointer;
}

.logoutBtn{
  height: 30px;
}

.logoutBtn:hover{
  cursor: pointer;
  color: #ffffff;
  background: #c9d0fe;
  padding-right: 0px;
}
.logoutBtn #lgout:hover{
  color: #fff;
}
.logoutBtn.active {
  background-color: #32406d;
  color: #fff; /* Color when active or hovered */
  padding-right: 0px;
}

.logoutBtn.active #lgout
{
  color: #fff;
}
.button {
  height: 30px;
  max-width: 180px;
  width: 100%;
  border: none;
  outline: none;
  color: #fff;
  border-radius: 5px;
  background-color: #32406d;
  transition: all 0.3s linear;
  cursor: pointer;
}

.button:hover {
  background-color: #555;
}

/* -------------------------- */
/* Table */
/* .container {
    position: relative;
    height: 88%;
    width: 98%;
    margin-top: 85px;
    padding: 10px 30px;
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(11.5px);
    -webkit-backdrop-filter: blur(11.5px);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.18);
    overflow: auto;
} */

/* ::-webkit-scrollbar {
  background: rgba(255, 255, 255, 0.25);
} */

/* ::-webkit-scrollbar-thumb {
  background-color: #42404034;
  border-radius: 10px;
} */

.container .table {
  position: relative;
  margin-top: 10px;
  /* min-height: 490px; */
  background-color: #fff;
  /* table-layout: fixed; */
}
/* 
.table thead {
    border-radius: 5px;
    font-weight: normal;
    border: none;
    border-collapse: collapse;
    width: 100%;
    max-width: 100%;
    white-space: nowrap;
    background-color: white;
}

.table td {
    text-align: left;
    border-right: 1px solid #f8f8f8;
    font-size: 12px;
    padding-left: 2px;
    font-weight: bold;
    color: #2e2e2e;
}

.table thead th {
    color: #ffffff;
    background: rgb(59, 59, 61);
    font-size: 13px;
} */

.table th {
  /* text-align: left;
    border-right: 1px solid #f8f8f8; */
  /* font-size: 16px; */
}

.table tr {
  /* height: 50px; */
  /* line-height: 0px; */
}

/* notification Badge */
/* .badge {
    position: absolute;
    top: -10px;
    right: -2px;
    padding: 5px 10px;
    border-radius: 50%;
    background: red;
    color: white;
} */

/* For desktop screens */
@media screen and (min-width: 1024px) {
  .filter-container > div {
    flex-basis: 16.66%;
    /* Each div takes up 1/6 of the container */
    margin-right: 10px;
    /* Maintain spacing between the filter items */
    margin-bottom: 10px;
    /* Maintain spacing between rows */
  }
}

/* For tablet screens */
@media screen and (max-width: 1023px) and (min-width: 768px) {
  .filter-container > div {
    flex-basis: 33.33%;
    /* Each div takes up 1/3 of the container */
    margin-right: 10px;
    /* Maintain spacing between the filter items */
    margin-bottom: 10px;
    /* Maintain spacing between rows */
  }
}

/* For mobile screens */
@media screen and (max-width: 767px) {
  .section {
    overflow: hidden;
  }

  .container {
    max-height: 85%;
    overflow: auto;
  }

  .heading h1 {
    font-size: 22px;
    margin-top: 10px;
    margin-right: 150px;
  }

  .logo {
    margin-left: 10px;
  }
}

/* logo styles */
.logo {
  height: 40px;
  width: 100px;
  margin-left: 50px;
}
/*upload photos*/
.profile .image-container img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
}

.options {
  width: 150px;
}

.options div {
  padding: 10px;
  cursor: pointer;
}

.options div:hover {
  background-color: #f0f0f0;
}

.reactEasyCrop_Container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  user-select: none;
  touch-action: none;
  cursor: move;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 250px;
  margin-top: 50px;
}
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

.modal-body {
  margin: 20px 0;
}

.modal-footer {
  text-align: center;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.modal-ok {
  background-color: #007bff;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.modal-ok:hover {
  background-color: #0056b3;
}

.options .img_u:hover {
  background-color: #32406d; /* Blue color on hover */
  color: #fff; /* White text color on hover */
}
@media screen and (max-height: 680px) and (min-width: 1000px) {
  .wrapper .sidebar {
    height: 100vh;
  }

  .wrapper .sidebar ul li a {
    display: block;
    padding: 3px 10px !important;
    border-bottom: 1px solid rgb(59, 59, 61);
    color: rgb(0, 0, 0);
    font-size: 12px !important;
    position: relative;
    text-decoration: none;
  }

  #lgout {
    font-size: 12px !important;
    padding-left: 10px !important;
  }

  .wrapper .sidebar {
    background: #ffffff;
    /* background: #161621; */
    position: fixed;
    top: 0;
    left: 0;
    width: 190px !important;
    height: 100vh;
    padding: 10px 0 !important;
    transition: all 0.5s ease;
  }

  /* profile pic */
  .wrapper .sidebar .profile {
    margin-bottom: 20px !important;
    text-align: center;
  }

  .wrapper .sidebar .profile img {
    display: block;
    width: 80px !important;
    height: 80px !important;
    border-radius: 50%;
    margin: 0 auto;
  }

  .wrapper .sidebar .profile h3 {
    color: #000000;
    margin: 10px 0 5px;
    font-size: 15px !important;
    font-weight: 500;
  }

  .wrapper .sidebar .profile p {
    color: rgb(15, 15, 15);
    font-size: 10px !important;
  }

  /* .profileHead1{
        font-size: "15px" !important;
    }
 
    .profileHead2{
        font-size: "14px";
     } */
}
.phone-icon {
  width: 40px;
  height: 40px;
  background-color: #32406D;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  margin-left: 20px;
  margin-top: 10px;
  cursor: pointer;
}

.chat-bubble {
  position: relative;
  background-color: #007bff;
  padding: 10px;
  border-radius: 10px;
  width:auto;
  max-width: 200px;
  margin: 10px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}
 
.chat-bubble::after {
  content: "";
  position: absolute;
  bottom: -10px; /* Adjust to move the tail down */
  left: 20px; /* Adjust to move the tail horizontally */
  width: 0;
  height: 0;
  border: 10px solid transparent;
  border-top-color: #007bff;
  border-bottom: 0;
  border-left: 0;
  margin-left: -10px;
}
@media only screen and (max-width: 542px) {
  .wrapper .sidebar {
    background: #ffffff;
    /* background: #161621; */
    position: fixed;
    top: 0;
    left: 0;
    width: 300px;
    height: 100vh;
    padding: 20px 0;
    transition: all 0.5s ease;
  }
  .sidebar {
    left: -300px;
  }
  .leftform{
    height: auto !important;
  }
}
@media screen and (min-width:320px) and (max-width: 375px) {

  #QuestionModal{
    width: 300px !important;
    height: auto !important;
  }
  .Modalleft{
    padding-left: 0px !important;
   
  }
  .Modalheading{
    font-size: 12px !important;
    justify-content: flex-start !important;
    padding-left: 0px !important;
  }

  .selectQ{
    width: 270px !important;
  }
}
@media screen and (min-width:375px) and (max-width: 425px) {
  #QuestionModal{
    width: 300px !important;
    height: auto !important;
  }
  .Modalleft{
    padding-left: 0px !important;
   
  }
  .Modalheading{
    font-size: 12px !important;
    justify-content: flex-start !important;
 
  }
  .modal-content_some{
    width: 300px !important;
    height: 100px !important;
  }
  .selectQ{
    width: 270px !important;
  }
}
.calendar-container .rbc-time-gutter {
  position: relative;
}
 
.custom-time-header {
  position: absolute;
  width: 100%;
  height: 30px; /* 30-minute line height */
  text-align: center;
  background-color: rgba(0, 255, 0, 0.1); /* Light green color */
  border-bottom: 1px solid #00FF00; /* Green line */
}
.rbc-time-slot:first-child {
  border-top:none; /* Example: Green line for the first time slot */
}
.rbc-day-slot .rbc-time-slot:first-child {
  border-bottom: 1px solid #ddd; /* Light grey line for slots */
}
 
.rbc-time-slot:hover {
  background-color: rgb(208, 219, 236); /* Green hover effect */
}
 
.rbc-time-content{
 border-top:none;
}
 
.rbc-event{
  font-size: 13px;
  color:"#fff";
 
}
.rbc-day-slot .rbc-event{
  display:block !important;
  font-size: 12px;
}
.rbc-day-slot .rbc-event-content {
  width: 100%;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 0px;
  flex: 1 1 1;
  /* display: none; */
  line-height: 1;
  height: 100%;
  min-height: 1em;
}
 
 
/* Adjust for 1-hour lines if needed */
.rbc-time-slot-1h {
  border-bottom: 2px solid #00FF00; 
}
 
 
.rbc-time-view .rbc-allday-cell {
  display: none;
}
 
.rbc-time-header.rbc-overflowing {
  margin-right: 3px !important;
 
  border-right: 2px solid yellow;
}
 
.rbc-time-content{
  border-top:1px solid #ddd !important;
}
 
 
 .rbc-time-slot {
  display: flex;
  align-items: center;
  justify-content: center; /* Optional: to center horizontally as well */
  height: 100%; /* Ensures the flex container takes the full height */
 
}
 
 .rbc-timeslot-group {
  min-height: 60px !important; /* Adjust as needed to ensure time labels are centered */
 
}
 
.rbc-time-slot .rbc-label {
  margin-top:30% ;
 
}
 
.rbc-day-slot .rbc-events-container{
  margin-right: 0px !important;
}

.recharts-default-legend{
  margin-top: 30px !important;
}

/* Genie effect */
