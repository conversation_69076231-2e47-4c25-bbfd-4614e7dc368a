import { useState, useRef, useEffect } from 'react';
import LeftNav from "../Components/LeftNav";
// import "../../Components/leftnav.css";
import TitleBar from "../Components/TitleBar";
import { ThreeDots } from "react-loader-spinner";
import { toast } from "react-toastify";
import Modal from "react-modal";
import './raise.css';

const RaiseIssue = () => {
  const [ images, setImages ] = useState([]); // Multiple images
  const [ issue, setIssue ] = useState('');
  const [ issueTitle, setIssueTitle ] = useState('');
  const [ issueType, setIssueType ] = useState('');
  const [ severity, setSeverity ] = useState('');
  const [ module, setModule ] = useState('');
  const [ waitForSubmission, setwaitForSubmission ] = useState(false);
  const [ modalMessage, setModalMessage ] = useState('');
  const [ isPasteFocused, setIsPasteFocused ] = useState(false);
  const pasteAreaRef = useRef(null);
  const [ isModalOpen, setIsModalOpen ] = useState(false);
  const [ showPreviousIssues, setShowPreviousIssues ] = useState(false);
  const [ previousIssues, setPreviousIssues ] = useState([]);
  const [ loadingIssues, setLoadingIssues ] = useState(false);

  // Load images from localStorage on mount
  useEffect(() => {
    const savedImages = localStorage.getItem('raise_issue_images');
    if (savedImages) {
      setImages(JSON.parse(savedImages));
    }
  }, []);

  // Save images to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('raise_issue_images', JSON.stringify(images));
  }, [ images ]);


  const handlePaste = (e) => {
    const items = e.clipboardData.items;

    for (let item of items) {
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        const reader = new FileReader();

        reader.onloadend = () => {
          setImages((prev) => [ ...prev, { file, base64: reader.result } ]);
        };

        reader.readAsDataURL(file);
      }
    }
  };


  const handleCommentChange = (e) => {
    setIssue(e.target.value);
  };
  const handleRemoveImage = (indexToRemove) => {
    const updated = images.filter((_, i) => i !== indexToRemove);
    setImages(updated);
    localStorage.setItem('raise_issue_images', JSON.stringify(updated));
  };


  const fetchPreviousIssues = async () => {
    setLoadingIssues(true);
    const userId = localStorage.getItem("user_id");

    try {
      // For now, using sample data. Replace with actual API call
      // const response = await fetch(`http://************:5002/get_user_issues/${userId}`);
      // const data = await response.json();

      // Sample data based on your database schema
      const sampleIssues = [
        {
          issue_id: 1,
          raised_by_name: "john",
          issue_title: "Login Page Not Loading",
          issue_description: "The login page takes too long to load and sometimes shows a blank screen",
          issue_type: "Bug",
          severity: "High",
          status: "In Progress",
          module_name: "Authentication",
          related_candidate_id: null,
          related_job_id: null,
          created_at: "2024-01-15 10:30:00",
          updated_at: "2024-01-16 14:20:00",
          assigned_to_user_id: 2,
          assigned_to_name: "John Smith",
          resolution_notes: null,
          is_internal: true
        },
        {
          issue_id: 2,
          raised_by_name: "jane",
          issue_title: "Dashboard Charts Not Displaying",
          issue_description: "Charts on the dashboard are not rendering properly in Chrome browser",
          issue_type: "Bug",
          severity: "Medium",
          status: "Open",
          module_name: "Dashboard",
          related_candidate_id: null,
          related_job_id: 123,
          created_at: "2024-01-10 09:15:00",
          updated_at: "2024-01-10 09:15:00",
          assigned_to_user_id: null,
          assigned_to_name: null,
          resolution_notes: null,
          is_internal: false
        },
        {
          issue_id: 3,
          raised_by_name: "john",
          issue_title: "Export Feature Not Working",
          issue_description: "Unable to export candidate data to Excel format",
          issue_type: "Feature Request",
          severity: "Low",
          status: "Resolved",
          module_name: "Reports",
          related_candidate_id: 456,
          related_job_id: null,
          created_at: "2024-01-05 16:45:00",
          updated_at: "2024-01-12 11:30:00",
          assigned_to_user_id: 3,
          assigned_to_name: "Sarah Johnson",
          resolution_notes: "Fixed export functionality and added additional format options",
          is_internal: true
        },
        {
          issue_id: 3,
          raised_by_name: "john",
          issue_title: "Export Feature Not Working",
          issue_description: "Unable to export candidate data to Excel format",
          issue_type: "Feature Request",
          severity: "Low",
          status: "Resolved",
          module_name: "Reports",
          related_candidate_id: 456,
          related_job_id: null,
          created_at: "2024-01-05 16:45:00",
          updated_at: "2024-01-12 11:30:00",
          assigned_to_user_id: 3,
          assigned_to_name: "Sarah Johnson",
          resolution_notes: "Fixed export functionality and added additional format options",
          is_internal: true
        },
        {
          issue_id: 3,
          raised_by_name: "john",
          issue_title: "Export Feature Not Working",
          issue_description: "Unable to export candidate data to Excel format",
          issue_type: "Feature Request",
          severity: "Low",
          status: "Resolved",
          module_name: "Reports",
          related_candidate_id: 456,
          related_job_id: null,
          created_at: "2024-01-05 16:45:00",
          updated_at: "2024-01-12 11:30:00",
          assigned_to_user_id: 3,
          assigned_to_name: "Sarah Johnson",
          resolution_notes: "Fixed export functionality and added additional format options",
          is_internal: true
        },
        {
          issue_id: 3,
          raised_by_name: "john",
          issue_title: "Export Feature Not Working",
          issue_description: "Unable to export candidate data to Excel format",
          issue_type: "Feature Request",
          severity: "Low",
          status: "Resolved",
          module_name: "Reports",
          related_candidate_id: 456,
          related_job_id: null,
          created_at: "2024-01-05 16:45:00",
          updated_at: "2024-01-12 11:30:00",
          assigned_to_user_id: 3,
          assigned_to_name: "Sarah Johnson",
          resolution_notes: "Fixed export functionality and added additional format options",
          is_internal: true
        }
      ];

      setPreviousIssues(sampleIssues);
    } catch (error) {
      console.error('Error fetching previous issues:', error);
      toast.error('Failed to load previous issues');
    } finally {
      setLoadingIssues(false);
    }
  };

  const handleToggleView = () => {
    if (!showPreviousIssues) {
      fetchPreviousIssues();
    }
    setShowPreviousIssues(!showPreviousIssues);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (waitForSubmission) return;

    // Validation
    if (!issueTitle.trim()) {
      toast.warn('Please enter an issue title.');
      return;
    }
    if (!issueType) {
      toast.warn('Please select an issue type.');
      return;
    }
    if (!severity) {
      toast.warn('Please select a severity level.');
      return;
    }
    if (!module) {
      toast.warn('Please select a module.');
      return;
    }
    if (!issue.trim()) {
      toast.warn('Please enter a detailed description of the issue.');
      return;
    }
    if (images.length === 0) {
      toast.warn('Please paste at least one screenshot.');
      return;
    }

    setwaitForSubmission(true);
    const userId = localStorage.getItem("user_id");

    const payload = {
      screenshots: images.map(img => img.base64.split(',')[ 1 ]),
      issue_title: issueTitle,
      issue_description: issue,
      issue_type: issueType,
      severity: severity,
      module_name: module,
      userId
    };

    try {
      const response = await fetch('http://************:5002/report_issue', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      const data = await response.json();

      if (data.status === "success") {
        // toast.success(data.message);
        setModalMessage(data.message);
        setwaitForSubmission(true);
        setIsModalOpen(true);
        // Reset all form fields
        setImages([]);
        setIssue('');
        setIssueTitle('');
        setIssueType('');
        setSeverity('');
        setModule('');
        localStorage.removeItem('raise_issue_images');

      }
      toast.error(data.error)
      setwaitForSubmission(false);
    } catch (error) {
      console.error('Error submitting issue:', error);
      setwaitForSubmission(false);
    } finally {
      setwaitForSubmission(false);
    }
  };

  return (
    <div className="wrapper">
      <LeftNav />
      <div className="section">
        <TitleBar />

        {/* Separate button outside the main container */}
        <div style={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: '35px',
          paddingRight: '20px',
          position: 'relative',
          zIndex: 100,
        }}>
          <button
            type="button"
            onClick={handleToggleView}
            style={{
              // padding: '10px 24px',
              padding: "5px",
              backgroundColor: '#32406d',
              color: 'white',
              border: '2px solid #32406d',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '600',
              boxShadow: '0 4px 8px rgba(0,0,0,0.2)',
              transition: 'all 0.3s ease',
              height: '40px'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#2a3558'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#32406d'}
          >
            {showPreviousIssues ? 'Back to Form ' : 'View Previous Issues'}
          </button>
        </div>

        <div className="raise-container" onPaste={handlePaste}>
          {showPreviousIssues && (
            <h3 style={{ marginBottom: "-15px", marginTop: "-15px" }}> Issues Raised</h3>
          )}

          {!showPreviousIssues ? (
            <form className="raise-form" onSubmit={handleSubmit}>
              <h2>Help & Support</h2>
              <div
                className="paste-area"
                ref={pasteAreaRef}
                style={{ overflow: "auto", height: "150px", cursor: isPasteFocused ? "text" : "pointer", }}
                tabIndex={0}
                onFocus={() => setIsPasteFocused(true)}
                onBlur={() => setIsPasteFocused(false)}
                onClick={() => pasteAreaRef.current.focus()}
              >
                {images.length > 0 ? (
                  images.map((img, index) => (
                    <div key={index} style={{ position: "relative" }}>
                      <img
                        key={index}
                        src={img.base64}
                        alt={`Screenshot ${index + 1}`}
                        className="preview-img"
                      />
                      <button
                        type="button"
                        onClick={() => handleRemoveImage(index)}
                        style={{
                          position: "absolute",
                          top: "-6px",
                          right: "-6px",
                          background: "red",
                          color: "white",
                          border: "none",
                          borderRadius: "50%",
                          width: "20px",
                          height: "20px",
                          cursor: "pointer",
                          fontSize: "12px",
                        }}
                        title="Remove screenshot"
                      >
                        ×
                      </button>
                    </div>
                  ))
                ) : (

                  <p
                    style={{
                      fontSize: "14px",
                      color: isPasteFocused ? "#888" : "red",
                    }}
                  >Click here and paste your screenshot(s) (Ctrl + V)</p>

                )}
              </div>

              {/* Issue Title Field */}
              <div className="form-group">
                <label htmlFor="issueTitle">Issue Title *</label>
                <input
                  type="text"
                  id="issueTitle"
                  placeholder="Enter a brief title for the issue"
                  value={issueTitle}
                  onChange={(e) => setIssueTitle(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #ddd',
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontFamily: 'inherit'
                  }}
                />
              </div>

              {/* Issue Type Field */}
              <div className="form-group">
                <label htmlFor="issueType">Issue Type *</label>
                <select
                  id="issueType"
                  value={issueType}
                  onChange={(e) => setIssueType(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #ddd',
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontFamily: 'inherit',
                    backgroundColor: 'white'
                  }}
                >
                  <option value="">Select Issue Type</option>
                  <option value="Bug">Bug</option>
                  <option value="Enhancement">Enhancement</option>
                  <option value="Feature Request">Feature Request</option>
                  <option value="Data Error">Data Error</option>
                  <option value="Performance">Performance</option>
                  <option value="UI/UX">UI/UX</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              {/* Severity Field */}
              <div className="form-group">
                <label htmlFor="severity">Severity *</label>
                <select
                  id="severity"
                  value={severity}
                  onChange={(e) => setSeverity(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #ddd',
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontFamily: 'inherit',
                    backgroundColor: 'white'
                  }}
                >
                  <option value="">Select Severity</option>
                  <option value="Low">Low - Minor issue, doesn&apos;t affect functionality</option>
                  <option value="Medium">Medium - Affects some functionality</option>
                  <option value="High">High - Major functionality affected</option>
                  <option value="Critical">Critical - System unusable</option>
                </select>
              </div>

              {/* Module Field */}
              <div className="form-group">
                <label htmlFor="module">Module *</label>
                <select
                  id="module"
                  value={module}
                  onChange={(e) => setModule(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #ddd',
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontFamily: 'inherit',
                    backgroundColor: 'white'
                  }}
                >
                  <option value="">Select Module</option>
                  <option value="Authentication">Authentication</option>
                  <option value="Dashboard">Dashboard</option>
                  <option value="Candidate Page">Candidate Page</option>
                  <option value="Reports">Reports</option>
                  <option value="User Management">User Management</option>
                  <option value="Job Management">Job Management</option>
                  <option value="Analytics">Analytics</option>
                  <option value="Settings">Settings</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="comment">Issue Description *</label>
                <textarea
                  id="comment"
                  style={{ height: "100px" }}
                  placeholder="Describe the issue in detail..."
                  value={issue}
                  onChange={handleCommentChange}
                />
              </div>

              <button type="submit" style={{ position: "relative", }} className="raise-button"        >
                {waitForSubmission ? "" : "Raise Issue"}
                <ThreeDots
                  wrapperClass="ovalSpinner"
                  wrapperStyle={{
                    position: "absolute",
                    top: "-3px",
                    right: "255px",
                    // background:"green"
                  }}
                  visible={waitForSubmission}
                  height="45"
                  width="45"
                  color="white"
                  ariaLabel="oval-loading"
                />
              </button>
            </form>
          ) : (
            <div className="issues-table-container">
              {loadingIssues ? (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                  <ThreeDots
                    height="50"
                    width="50"
                    color="#32406d"
                    ariaLabel="loading"
                  />
                  <p>Loading previous issues...</p>
                </div>
              ) : previousIssues.length === 0 ? (
                <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                  <h3>No previous issues found</h3>
                  <p>You haven&#39;t submitted any issues yet.</p>
                </div>
              ) : (

                <div style={{
                  overflowX: 'auto',
                  overflowY: 'auto',
                  maxHeight: '490px',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                }}>
                  <table style={{
                    width: '100%',
                    minWidth: '1200px',
                    borderCollapse: 'collapse',
                    backgroundColor: 'white'
                  }}>
                    <thead style={{ position: 'sticky', top: 0, zIndex: 10 }}>
                      <tr style={{ backgroundColor: '#32406d', color: 'white', height: '50px' }}>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600' }}>Issue ID</th>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600' }}>Raised By</th>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600', }}>Issue Title</th>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600', }}>Issue Description</th>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600' }}>Issue Type</th>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600' }}>Severity</th>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600' }}>Status</th>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600' }}>Module</th>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600' }}>Created on</th>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600' }}>Last Updated</th>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600' }}>Assigned To</th>
                        <th style={{ padding: '12px 8px', textAlign: 'left', borderBottom: '1px solid #ddd', fontSize: '13px', fontWeight: '600', }}>Resolution Notes</th>
                      </tr>
                    </thead>
                    <tbody>
                      {previousIssues.map((issue, index) => (
                        <tr key={issue.issue_id} style={{
                          backgroundColor: index % 2 === 0 ? '#f8f9fa' : 'white',
                          borderBottom: '1px solid #eee',
                          minHeight: '60px'
                        }}>
                          <td style={{ padding: '12px 8px', borderBottom: '1px solid #ddd', fontSize: '13px', verticalAlign: 'top' }}>
                            #{issue.issue_id}
                          </td>
                          <td style={{ padding: '12px 8px', borderBottom: '1px solid #ddd', fontSize: '13px', verticalAlign: 'top' }}>
                            {issue.raised_by_name}
                          </td>
                          <td style={{
                            padding: '12px 8px',
                            borderBottom: '1px solid #ddd',
                            fontSize: '13px',
                            maxWidth: '200px',
                            verticalAlign: 'top'
                          }}>
                            <div
                              style={{
                                fontWeight: '500',
                                lineHeight: '1.4',
                                wordWrap: 'break-word',
                                whiteSpace: 'normal'
                              }}
                              title={issue.issue_title}
                            >
                              {issue.issue_title}
                            </div>
                          </td>
                          <td style={{
                            padding: '12px 8px',
                            borderBottom: '1px solid #ddd',
                            fontSize: '13px',
                            maxWidth: '250px',
                            verticalAlign: 'top'
                          }}>
                            <div
                              style={{
                                lineHeight: '1.4',
                                wordWrap: 'break-word',
                                whiteSpace: 'normal',
                                maxHeight: '60px',
                                overflow: 'hidden'
                              }}
                              title={issue.issue_description}
                            >
                              {issue.issue_description}
                            </div>
                          </td>
                          <td style={{ padding: '12px 8px', borderBottom: '1px solid #ddd', fontSize: '13px', verticalAlign: 'top' }}>
                            <span style={{
                              padding: '4px 8px',
                              borderRadius: '8px',
                              fontSize: '11px',
                              fontWeight: '500',
                              backgroundColor: '#e0e7ff',
                              color: '#3730a3'
                            }}>
                              {issue.issue_type}
                            </span>
                          </td>
                          <td style={{ padding: '12px 8px', borderBottom: '1px solid #ddd', fontSize: '13px', verticalAlign: 'top' }}>
                            <span style={{
                              padding: '4px 8px',
                              borderRadius: '8px',
                              fontSize: '11px',
                              fontWeight: '500',
                              backgroundColor:
                                issue.severity === 'High' ? '#fee2e2' :
                                  issue.severity === 'Medium' ? '#fef3c7' :
                                    issue.severity === 'Low' ? '#d1fae5' : '#f3f4f6',
                              color:
                                issue.severity === 'High' ? '#dc2626' :
                                  issue.severity === 'Medium' ? '#d97706' :
                                    issue.severity === 'Low' ? '#059669' : '#374151'
                            }}>
                              {issue.severity}
                            </span>
                          </td>
                          <td style={{ padding: '12px 8px', borderBottom: '1px solid #ddd', fontSize: '13px', verticalAlign: 'top' }}>
                            <span style={{
                              padding: '4px 8px',
                              borderRadius: '8px',
                              fontSize: '11px',
                              fontWeight: '500',
                              backgroundColor:
                                issue.status === 'Open' ? '#fef3c7' :
                                  issue.status === 'In Progress' ? '#dbeafe' :
                                    issue.status === 'Resolved' ? '#d1fae5' :
                                      issue.status === 'Closed' ? '#f3f4f6' : '#f3f4f6',
                              color:
                                issue.status === 'Open' ? '#92400e' :
                                  issue.status === 'In Progress' ? '#1e40af' :
                                    issue.status === 'Resolved' ? '#065f46' :
                                      issue.status === 'Closed' ? '#374151' : '#374151'
                            }}>
                              {issue.status}
                            </span>
                          </td>
                          <td style={{ padding: '12px 8px', borderBottom: '1px solid #ddd', fontSize: '13px', verticalAlign: 'top' }}>
                            {issue.module_name}
                          </td>
                          <td style={{ padding: '12px 8px', borderBottom: '1px solid #ddd', fontSize: '13px', verticalAlign: 'top' }}>
                            {new Date(issue.created_at).toLocaleDateString()}
                          </td>
                          <td style={{ padding: '12px 8px', borderBottom: '1px solid #ddd', fontSize: '13px', verticalAlign: 'top' }}>
                            {new Date(issue.updated_at).toLocaleDateString()}
                          </td>
                          <td style={{ padding: '12px 8px', borderBottom: '1px solid #ddd', fontSize: '13px', verticalAlign: 'top' }}>
                            {issue.assigned_to_name || 'Unassigned'}
                          </td>
                          <td style={{
                            padding: '12px 8px',
                            borderBottom: '1px solid #ddd',
                            fontSize: '13px',
                            maxWidth: '200px',
                            verticalAlign: 'top'
                          }}>
                            <div
                              style={{
                                lineHeight: '1.4',
                                wordWrap: 'break-word',
                                whiteSpace: 'normal',
                                maxHeight: '60px',
                                overflow: 'hidden'
                              }}
                              title={issue.resolution_notes || 'No resolution notes'}
                            >
                              {issue.resolution_notes || '-'}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}
        </div>



        <Modal
          isOpen={isModalOpen}
          onRequestClose={() => setIsModalOpen(false)} // Optional for escape key and click outside
          style={{
            overlay: {
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              zIndex: 9999,
            },
            content: {
              top: '50%',
              left: '50%',
              right: 'auto',
              bottom: 'auto',
              transform: 'translate(-50%, -50%)',
              width: '400px',
              borderRadius: '12px',
              padding: '30px 20px',
              backgroundColor: '#fff',
              boxShadow: '0 8px 20px rgba(0, 0, 0, 0.2)',
              transition: 'all 0.3s ease-in-out',
            }
          }}
        >
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: '600', color: '#2d3748' }}>
              Success!
            </div>
            <p style={{ color: '#4a5568', marginTop: '12px', fontSize: '16px' }}>
              {modalMessage}
            </p>
          </div>

          <div style={{ display: 'flex', justifyContent: 'center', marginTop: '25px' }}>
            <button
              onClick={() => setIsModalOpen(false)}
              style={{
                padding: '10px 24px',
                background: 'linear-gradient(135deg, #38a169, #48bb78)',
                color: '#fff',
                border: 'none',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                boxShadow: '0 2px 8px rgba(56, 161, 105, 0.3)',
                transition: 'background 0.3s ease',
              }}
              onMouseOver={(e) =>
                (e.target.style.background = 'linear-gradient(135deg, #2f855a, #38a169)')
              }
              onMouseOut={(e) =>
                (e.target.style.background = 'linear-gradient(135deg, #38a169, #48bb78)')
              }
            >
              OK
            </button>
          </div>
        </Modal>

      </div>
    </div>
  );
};

export default RaiseIssue;
